package UI.setting
{
   import UI.UIOrder;
   import UI.UIShow;
   import UI.base.btnList.BtnBox;
   import UI.base.button.NormalBtn;
   import UI.count.CountCtrl;
   import UI.pay.PayCtrl;
   import UI.pet.PetUI;
   import com.adobe.serialization.json.JSON2;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.active.ActiveData;
   import dataAll._app.head.HeadData;
   import dataAll._app.head.define.HeadDefine;
   import dataAll._app.head.define.HeadDefineGroup;
   import dataAll._app.love.LoveData;
   import dataAll._app.space.craft.CraftData;
   import dataAll._app.union.define.MilitaryDefine;
   import dataAll._app.worldMap.define.MapMode;
   import dataAll._app.parts.PartsDataGroup;
   import dataAll._data.ConstantDefine;
   import dataAll._player.more.MorePlayerData;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.arms.save.ArmsSave;
   import gameAll.arms.GameArmsCtrl;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipFatherDefine;
   import dataAll.equip.save.EquipSave;
   import dataAll.equip.weapon.WeaponDefine;
   import dataAll.pet.PetData;
   import dataAll.pet.gene.save.GeneSave;
   import dataAll.skill.HeroSkillData;
   import dataAll.things.define.ThingsDefine;
   import dataAll.things.ThingsData;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.events.TimerEvent;
   import flash.system.System;
   import flash.text.TextField;
   import flash.utils.Timer;
   import gameAll.body.IO_NormalBody;
   import gameAll.hero.HeroBody;
   import gameAll.level.PlayMode;
   import gameAll.level.data.OverLevelShow;
   import w_test.drop.LevelDropCount;
   
   public class SettingGamingBox extends BtnBox
   {
      
      public var Title:String;
      
      public var NumericalValue:int;
      
      public var Name:String;
      
      public var ArrColor0:Array = ["white","green","blue","purple","orange","red","black","darkgold","purgold"];
      
      public var ArrColor1:Array = ["白","绿","蓝","紫","橙","红","黑","暗金","紫金"];
      
      public var labelTag:Sprite;
      
      public var verTxt:TextField;
      
      private var saveTimer:Timer = new Timer(1000);
      
      private var SAVE_T:int = 60;
      
      private var save_t:int = -1;
      
      public function SettingGamingBox()
      {
         super();
         this.saveTimer.addEventListener(TimerEvent.TIMER,this.saveTimerFun);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         elementNameArr = ["labelTag","verTxt"];
         super.setImg(img0);
         getBtn("resume").setName("切换存档");
         getBtn("restart").setName("图内修改");
         getBtn("main").setName("退出关卡");
         getBtn("save").setName("保存存档");
         getBtn("about").setName("自定义后台");
         getBtn("pass").setName("关于");
         this.verTxt.text = ConstantDefine.getAllVer();
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function fleshData() : void
      {
         this.saveTimerFun();
         var gamingB0:Boolean = Gaming.LG.state != "no";
         if(gamingB0)
         {
            showBtnArr(["save","resume","restart","main"]);
            getBtn("restart").actived = Gaming.LG.nowLevel.getCanRestartB();
            if(LevelDropCount.testB)
            {
               getBtn("restart").actived = true;
            }
         }
         else
         {
            showBtnArr(["save","about","pass","resume"]);
         }
         getBtn("resume").setName("切换存档");
         getBtn("pass").setName("关于");
         this.setBtnNameByMapModel();
         this.volumeBar.setPer(Gaming.PG.save.setting.getValue("volume"));
      }
      
      private function setBtnNameByMapModel() : void
      {
         var levelName0:String = this.getLevelName();
         getBtn("restart").setName("图内修改");
         getBtn("main").setName("退出" + levelName0);
      }
      
      private function getLevelName() : String
      {
         var model0:String = Gaming.LG.mapMode;
         if(model0 == MapMode.ENDLESS)
         {
            return "无尽模式";
         }
         return "关卡";
      }
      
      private function barChange(v0:Number, label0:String) : void
      {
         if(Gaming.PG.save)
         {
            Gaming.PG.save.setting.setValue(label0,v0);
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var mainStr0:String = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         var levelName0:String = this.getLevelName();
         var model0:String = Gaming.LG.mapMode;
         if(btn0.label == "resume")
         {
            Gaming.uiGroup.loginUI.show();
         }
         else if(btn0.label == "restart")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("通关当前[00] 重玩关卡[01]  主角AI[02] 秒杀队友[03]\n秒杀全图[04] 寄生首领[05] 寄生宠物[06] 副手怒气[07]\n","00",this.InMapCheating);
         }
         else if(btn0.label == "main")
         {
            mainStr0 = Gaming.LG.nowLevel.define.info.overWarn;
            if(mainStr0 == "")
            {
               mainStr0 = "确定要退出" + levelName0 + "？";
            }
            if(Gaming.LG.mode == PlayMode.ARENA && !Gaming.LG.nowLevel.dat.winB)
            {
               mainStr0 = "如果退出本关，那么本场竞技挑战就会被判定为失败\n你确定要退出关卡?";
            }
            else if(model0 == MapMode.ENDLESS)
            {
            }
            Gaming.uiGroup.alertBox.showCheck(mainStr0,"yesAndNo",0,this.mainFun);
         }
         else if(btn0.label == "save")
         {
            UIOrder.save(true,false,false);
            UIOrder.save();
         }
         else if(btn0.label == "about")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("人物类[00]  背包类[01]  地图类[02]  物品类[03]\n成就类[04]  尸宠类[05]  任务类[06]  货币类[07]\n厨艺类[08]  魂卡类[09]  飞船类[10]  竞技场[11]\n军队类[12]  时间类[13]  存档类[14]  " + ComMethod.color("下一页[15]","#fd397b") + "\n","",this.Allcheating);
         }
         else if(btn0.label == "pass")
         {
            Gaming.uiGroup.alertBox.showSuccess("By 楠楠：2914596415");
         }
      }
      
      public function Allcheating(str0:String) : void
      {
         var Arr_AllMenu:Array = new Array();
         Arr_AllMenu = str0.split("*",str0.length);
         this.Title = Arr_AllMenu[0];
         if(this.Title == "人物类" || this.Title == "00")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("人物昵称[00*昵称]  人物等级[01*数值]\n人物经验[02*数值]  添加称号[03*称号]\n巅峰等级[04*数值]  巅峰经验[05*数值]\n职务等级[06*数值]  职务经验[07*数值]\n  活跃度[08*数值]  " + ComMethod.color("|下一页[09*可空]|","#fd397b") + "\n","00*楠楠",this.HeroCheating);
         }
         if(this.Title == "背包类" || this.Title == "01")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("武器背包[00*数值]  装备背包[01*数值]\n物品背包[02*数值]  基因背包[03*数值]\n零件背包[04*数值]  技能背包[05*数值]\n尸宠背包[06*数值]  魂卡背包[07*数值]\n武器仓库[08*数值]  " + ComMethod.color("|下一页[09*可空]|","#fd397b") + "\n","",this.BagCheating);
         }
         if(this.Title == "地图类" || this.Title == "02")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("解锁所有地图[00*可空]  通关所有地图[01*可空]\n解锁所有秘境[02*可空]  设置秘境钥匙[03*数值]\n","00",this.MapCheating);
         }
         if(this.Title == "物品类" || this.Title == "03")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput(ComMethod.color("--------武器类--------","#fd397b") + "\n添加指定武器[00*名字*等级] (支持所有武器)\n添加所有稀有武器[01*等级]\n添加所有黑色武器[02*等级]\n" + ComMethod.color("|下一页[03*可空]|","#fd397b") + "\n","00*光锥*99",this.ThingCheating);
         }
         if(this.Title == "货币类" || this.Title == "07")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput(ComMethod.color("--------特殊货币--------","#fd397b") + "\n设置纪念币[00*数量]\n设置十年币[01*数量]\n设置零件券[02*数量]\n设置粽子[03*数量]\n设置银币[04*数量]\n设置积分[05*数量]\n设置黄金[06*数量]\n设置万能球[07*数量]\n黄金充值[08*数量]\n设置小南瓜[09*数量]\n","00*99999",this.CurrencyCheating);
         }
         if(this.Title == "成就类" || this.Title == "04")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput(ComMethod.color("解锁全部成就[00*可空]","#fd397b") + "\n杀敌数量[01*数值]  boss数量[02*数值]\n死亡数量[03*数值]  副手击杀[04*数值]\n载具击杀[05*数值]  武器掉落[06*数值]\n装备掉落[07*数值]  " + ComMethod.color("|下一页[08*可空]|","#fd397b") + "\n","00",this.AchieveCheating);
         }
         if(this.Title == "尸宠类" || this.Title == "05")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("尸宠昵称[00*昵称]  尸宠等级[01*数值]\n尸宠经验[02*数值]  头部防御[03*数值]\n  战斗力[04*数值]    生命力[05*数值]\n随机基因体[06*颜色]\n","00*楠楠",this.PetCheating);
         }
         if(this.Title == "任务类" || this.Title == "06")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("解锁主线任务[00]  解锁任务系统[01]\n接取当前任务[02]  完成当前任务[03]\n","00",this.TaskCheating);
         }

         if(this.Title == "厨艺类" || this.Title == "08")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("食材数量[00*数值]\n总厨艺值[01*数值]\n","00*999999",this.FoodCheating);
         }
         if(this.Title == "魂卡类" || this.Title == "09")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("普通已抽次数[00*数值]  高级已抽次数[01*数值]\n普通抽卡次数[02*数值]  " + ComMethod.color("无限高级抽卡[03*可空]","#fd397b") + "\n","03",this.BossCardCheating);
         }
         if(this.Title == "飞船类" || this.Title == "10")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("设置飞船等级[00*数值]\n添加飞船经验[01*数值]\n","00*999",this.SpaceCheating);
         }
         if(this.Title == "竞技场" || this.Title == "11")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("设置竞技场次数[00*数值]\n设置竞技场分数[01*数值]\n","01*3650",this.ArenaCheating);
         }
         if(this.Title == "军队类" || this.Title == "12")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("完成军队任务[00*可空]  设置军衔等级[01*数值]\n快速升级军队[02*等级]  设置个人贡献[03*数值]\n清除军队数据[04*可空]  取消争霸限制[05*可空]\n设置创建军队所需黄金[06*数值]\n","",this.UnionCheating);
         }
         if(this.Title == "时间类" || this.Title == "13")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("时间加速倍数[00*倍数]  设置游戏帧数[01*帧数]\n设置新的一天[02*可空]  设置新的一周[03*可空]\n本地时间开关[04*可空]  清除双倍时间[05*可空]\n","01*60",this.TimeCheating);
         }
         if(this.Title == "存档类" || this.Title == "14")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("查询异常原因[00]  初始化本存档[01]\n复制存档数据[02]  修复存档数据[03]\n解除存档异常[04]  设置存档异常[05]","00",this.SaveCheating);
         }
         if(this.Title == "15")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("界面类[00]  系统类[01]  战斗类[02]  特效类[03]\n调试类[04]  数据类[05]  网络类[06]  " + ComMethod.color("返回首页[07]","#fd397b") + "\n","",this.Allcheating_Two);
         }
      }
      
      public function HeroCheating(str0:String) : void
      {
         var Arr_Hero:Array = new Array();
         Arr_Hero = str0.split("*",str0.length);
         this.Title = Arr_Hero[0];
         this.Name = Arr_Hero[1];
         this.NumericalValue = int(Arr_Hero[1]);
         var head:String = "";
         if(this.Title == "人物昵称" || this.Title == "00")
         {
            Gaming.PG.DATA.base.save.playerName = this.Name;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("人物昵称","#fd397b") + "为" + ComMethod.color(this.Name,"#FFFF00") + "成功!");
         }
         if(this.Title == "人物等级" || this.Title == "01")
         {
            Gaming.PG.DATA.base.save.level = Number(this.NumericalValue);
            Gaming.uiGroup.mainUI.show();
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("人物等级","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "级成功!");
         }
         if(this.Title == "人物经验" || this.Title == "02")
         {
            Gaming.PG.DATA.base.save.exp = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("人物经验","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "添加称号" || this.Title == "03")
         {
            if(this.Name == "全部" || this.Name == "所有")
            {
               this.AddAllHead();
               Gaming.uiGroup.alertBox.showSuccess("添加" + ComMethod.color("全部人物称号","#fd397b") + "成功!");
            }
            else
            {
               if(this.Name == "枪械制造大师")
               {
                  head = "armsSkinCreator";
               }
               if(this.Name == "群组达人")
               {
                  head = "bbs23";
               }
               else if(this.Name == "九周年快乐")
               {
                  head = "anniver9";
               }
               else if(this.Name == "成就之皇")
               {
                  head = "achieveKing";
               }
               else if(this.Name == "八周年快乐")
               {
                  head = "anniver8";
               }
               else if(this.Name == "十二生肖")
               {
                  head = "zodiac12";
               }
               else if(this.Name == "霞光天军")
               {
                  head = "battle3";
               }
               else if(this.Name == "霞光雄狮")
               {
                  head = "battle2";
               }
               else if(this.Name == "霞光劲旅")
               {
                  head = "battle1";
               }
               else if(this.Name == "愚人欢乐")
               {
                  head = "joyousFool";
               }
               else if(this.Name == "爆枪七周年")
               {
                  head = "anniver7";
               }
               else if(this.Name == "单身狗")
               {
                  head = "singleAF";
               }
               else if(this.Name == "愚人快乐")
               {
                  head = "happyFool";
               }
               else if(this.Name == "爆枪六周年")
               {
                  head = "anniver6";
               }
               else if(this.Name == "六一快乐")
               {
                  head = "childrenDay";
               }
               else if(this.Name == "爆枪五周年")
               {
                  head = "anniver5";
               }
               else if(this.Name == "爆枪四周年")
               {
                  head = "anniver4";
               }
               else if(this.Name == "爆枪三周年")
               {
                  head = "anniver3";
               }
               else if(this.Name == "七夕眷侣")
               {
                  head = "qixi";
               }
               else if(this.Name == "粽叶飘香")
               {
                  head = "zongzi";
               }
               else if(this.Name == "春节快乐")
               {
                  head = "happySpring";
               }
               else if(this.Name == "秘境征服者")
               {
                  head = "widerAll";
               }
               else if(this.Name == "超能英雄")
               {
                  head = "superHero";
               }
               else if(this.Name == "特种奇兵")
               {
                  head = "specialSoldiers";
               }
               else if(this.Name == "爆头王")
               {
                  head = "headshot_20";
               }
               else if(this.Name == "踏平霞光")
               {
                  head = "goddiff_30";
               }
               else if(this.Name == "死神")
               {
                  head = "death";
               }
               else if(this.Name == "武器收藏家")
               {
                  head = "rareArms_5";
               }
               else if(this.Name == "假的大结局")
               {
                  head = "fakeFinale";
               }
               else if(this.Name == "战神")
               {
                  head = "dpsTop_1";
               }
               else if(this.Name == "竞技之王")
               {
                  head = "arena_1";
               }
               else if(this.Name == "步枪之王")
               {
                  head = "rifle_1";
               }
               else if(this.Name == "狙击之王")
               {
                  head = "sniper_1";
               }
               else if(this.Name == "散弹之王")
               {
                  head = "shotgun_1";
               }
               else if(this.Name == "手枪之王")
               {
                  head = "pistol_1";
               }
               else if(this.Name == "火炮之王")
               {
                  head = "rocket_1";
               }
               else if(this.Name == "十项全能")
               {
                  head = "almighty_10";
               }
               else if(this.Name == "兵器大亨")
               {
                  head = "weapon_4";
               }
               else if(this.Name == "装置大亨")
               {
                  head = "device_4";
               }
               else if(this.Name == "成就大亨")
               {
                  head = "achieve_70";
               }
               else if(this.Name == "成就之王")
               {
                  head = "achieve_123";
               }
               else if(this.Name == "独霸一方")
               {
                  head = "dominating";
               }
               else if(this.Name == "公会是我家")
               {
                  head = "unionIsMyHome";
               }
               else if(this.Name == "我要升级")
               {
                  head = "wantUpgrade";
               }
               else if(this.Name == "闲着蛋疼")
               {
                  head = "petEvo_4";
               }
               else if(this.Name == "载具新时代")
               {
                  head = "vehicleEvo_4";
               }
               else if(this.Name == "学霸")
               {
                  head = "ask_5";
               }
               else if(this.Name == "全能人")
               {
                  head = "heroSkill_21";
               }
               else if(this.Name == "武器锻造家")
               {
                  head = "armsRemake100";
               }
               else if(this.Name == "装备锻造家")
               {
                  head = "equipRemake100";
               }
               else if(this.Name == "爆枪突击")
               {
                  head = "baoqiang";
               }
               else if(this.Name == "佣兵之王")
               {
                  head = "gameKing";
               }
               else if(this.Name == "佣兵精英")
               {
                  head = "gameSuper";
               }
               if(head != "")
               {
                  Gaming.PG.da.head.save.addHead(head,Gaming.api.save.getNowServerDate().getStr());
                  Gaming.uiGroup.alertBox.showSuccess("添加" + ComMethod.color("人物称号","#fd397b") + ComMethod.color(this.Name,"#FFFF00") + "成功!");
               }
            }
         }
         if(this.Title == "巅峰等级" || this.Title == "04")
         {
            Gaming.PG.da.peak.save.lv = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("巅峰等级","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "级成功!");
         }
         if(this.Title == "巅峰经验" || this.Title == "05")
         {
            Gaming.PG.da.peak.save.exp = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("巅峰经验","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "职务等级" || this.Title == "06")
         {
            Gaming.PG.da.post.save.postLv = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("职务等级","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "级成功!");
         }
         if(this.Title == "职务经验" || this.Title == "07")
         {
            Gaming.PG.da.post.save.postExp = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("职务经验","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "活跃度" || this.Title == "08")
         {
            ActiveData.TEST_ACTIVE = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("活跃度","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "09")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("  添加队友[00*昵称]\n  设置功勋[01*数值]\n设置好感度[02*数值]\n技能熟练度[03*数值]\n","00*小樱",this.MoreCheating);
         }
      }
      
      public function MoreCheating(str0:String) : void
      {
         var value0:int = 0;
         var loveData0:LoveData = null;
         var pd0:MorePlayerData = Gaming.PG.DATA as MorePlayerData;
         var da0:HeroSkillData = Gaming.uiGroup.skillUI.upgradeBox.nowData;
         var Arr_More:Array = new Array();
         Arr_More = str0.split("*",str0.length);
         this.Title = Arr_More[0];
         this.Name = Arr_More[1];
         this.NumericalValue = int(Arr_More[1]);
         var hero:String = "";
         if(this.Title == "添加队友" || this.Title == "00")
         {
            if(this.Name == "小樱")
            {
               hero = "Girl";
            }
            else if(this.Name == "心零")
            {
               hero = "XinLing";
            }
            else if(this.Name == "文杰表哥")
            {
               hero = "WenJie";
            }
            else if(this.Name == "藏师将军")
            {
               hero = "ZangShi";
            }
            else if(this.Name == "摩卡")
            {
               hero = "Mocha";
            }
            else if(this.Name == "丛林特种兵")
            {
               hero = "Jungle";
            }
            else if(this.Name == "雇佣兵")
            {
               hero = "ATian";
            }
            else if(this.Name == "僵尸突击兵")
            {
               hero = "ZombieSoldier";
            }
            else if(this.Name == "制毒师")
            {
               hero = "Doctor";
            }
            else if(this.Name == "制毒师2")
            {
               hero = "Doctor2";
            }
            else if(this.Name == "沙漠特种兵")
            {
               hero = "DesertCommando";
            }
            else if(this.Name == "僵尸空降兵")
            {
               hero = "ZombieAirborne";
            }
            else if(this.Name == "僵尸狙击兵")
            {
               hero = "ZombieShoot";
            }
            else if(this.Name == "僵尸暴枪兵")
            {
               hero = "ZombieViolence";
            }
            else if(this.Name == "僵尸空军总管")
            {
               hero = "ZombieAirForce";
            }
            else if(this.Name == "僵尸炮兵总管")
            {
               hero = "ZombieShell";
            }
            else if(this.Name == "防暴僵尸")
            {
               hero = "ZombieRiotl";
            }
            else if(this.Name == "火炮僵尸王")
            {
               hero = "ZombieBoomKing";
            }
            else if(this.Name == "天鹰特种兵")
            {
               hero = "TUNCommando";
            }
            else if(this.Name == "天鹰空降兵")
            {
               hero = "TUNAirborne";
            }
            else if(this.Name == "鬼目射手")
            {
               hero = "LingShooter";
            }
            else if(this.Name == "亚瑟")
            {
               hero = "Arthur";
            }
            else if(this.Name == "狂战射手")
            {
               hero = "FightShooter";
            }
            else if(this.Name == "天鹰小美")
            {
               hero = "XiaoMei";
            }
            else if(this.Name == "奇皇博士")
            {
               hero = "QiHuang";
            }
            else if(this.Name == "鬼影战士")
            {
               hero = "GhostSoldier";
            }
            else if(this.Name == "鬼爵")
            {
               hero = "GhostDuke";
            }
            else if(this.Name == "黑客")
            {
               hero = "Hacker";
            }
            else if(this.Name == "战神")
            {
               hero = "Madboss";
            }
            else if(this.Name == "沃龙")
            {
               hero = "Wanda";
            }
            Gaming.PG.da.moreBag.addByUnitName(hero);
            Gaming.PG.da.more.swapByOther(Gaming.PG.da.moreBag,1,0);
            UIShow.main();
            Gaming.uiGroup.alertBox.showSuccess("添加队友" + ComMethod.color(this.Name,"#FFFF00") + "成功!");
         }
         if(this.Title == "设置功勋" || this.Title == "01")
         {
            pd0.partner.save.exploit = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color(pd0.heroData.def.cnName,"#FFFF00") + "的功勋值为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "设置好感度" || this.Title == "02")
         {
            loveData0 = pd0.love;
            value0 = this.NumericalValue - loveData0.save.value;
            loveData0.addValue(value0);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color(pd0.heroData.def.cnName,"#FFFF00") + "的" + loveData0.getCn() + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "技能熟练度" || this.Title == "03")
         {
            da0.save.profi = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color(da0.save.getDefine().cnName,"#FFFF00") + "的熟练度为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
      }
      
      public function AddAllHead() : void
      {
         try
         {
            var headData:HeadData = Gaming.PG.da.head;
            var headDefineGroup:HeadDefineGroup = Gaming.defineGroup.head;
            var addedCount:int = 0;
            var currentTime:String = "";

            // 获取正确的时间字符串
            try
            {
               currentTime = Gaming.api.save.getNowServerDate().getStr();
            }
            catch(timeError:Error)
            {
               // 如果获取服务器时间失败，使用简单的时间格式
               var now:Date = new Date();
               currentTime = now.getFullYear() + "-" + (now.getMonth() + 1) + "-" + now.getDate();
               trace("使用本地时间: " + currentTime);
            }

            if(headData && headData.save && headDefineGroup && currentTime != "")
            {
               // 遍历所有称号定义
               var allHeadDefines:Array = headDefineGroup.arr;
               for each(var headDefine:HeadDefine in allHeadDefines)
               {
                  if(headDefine && headDefine.name)
                  {
                     // 检查是否已经拥有该称号
                     if(!headData.save.panHaveHead(headDefine.name))
                     {
                        // 添加称号
                        headData.save.addHead(headDefine.name, currentTime);
                        addedCount++;
                        trace("添加称号: " + headDefine.cnName + " (" + headDefine.name + ")");
                     }
                  }
               }

               // 刷新称号UI
               if(Gaming.uiGroup.headUI && Gaming.uiGroup.headUI.visible)
               {
                  Gaming.uiGroup.headUI.haveBoard.fleshData();
                  Gaming.uiGroup.headUI.noBoard.fleshData();
                  Gaming.uiGroup.headUI.honorBoard.fleshData();
               }

               trace("成功添加 " + addedCount + " 个称号");
            }
            else
            {
               trace("称号系统检查失败 - headData: " + (headData != null) + ", headData.save: " + (headData && headData.save != null) + ", headDefineGroup: " + (headDefineGroup != null) + ", currentTime: " + currentTime);
            }
         }
         catch(e:Error)
         {
            trace("添加称号错误: " + e.message + "\n堆栈: " + e.getStackTrace());
         }
      }
      
      public function BagCheating(str0:String) : void
      {
         var Arr_Bag:Array = new Array();
         Arr_Bag = str0.split("*",str0.length);
         this.Title = Arr_Bag[0];
         this.NumericalValue = int(Arr_Bag[1]);
         if(this.Title == "武器背包" || this.Title == "00")
         {
            Gaming.PG.da.armsBag.saveGroup.unlockTo(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("武器背包数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "装备背包" || this.Title == "01")
         {
            Gaming.PG.da.equipBag.saveGroup.unlockTo(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("装备背包数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "物品背包" || this.Title == "02")
         {
            Gaming.PG.da.thingsBag.saveGroup.unlockTo(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("物品背包数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "基因背包" || this.Title == "03")
         {
            Gaming.PG.da.geneBag.saveGroup.unlockTo(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("基因背包数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "零件背包" || this.Title == "04")
         {
            Gaming.PG.da.partsBag.saveGroup.unlockTo(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("零件背包数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "技能背包" || this.Title == "05")
         {
            Gaming.PG.da.skill.saveGroup.unlockTo(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("技能背包数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "尸宠背包" || this.Title == "06")
         {
            Gaming.PG.da.pet.saveGroup.lockLen = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("尸宠背包数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "魂卡背包" || this.Title == "07")
         {
            Gaming.PG.save.bossCard.bag = Number(this.NumericalValue - 80);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("魂卡背包数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "武器仓库" || this.Title == "08")
         {
            Gaming.PG.da.armsHouse.saveGroup.unlockTo(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("武器仓库数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "09")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("装备仓库[10*数值]  清除武器[11*可空]\n清除装备[12*可空]  清除物品[13*可空]\n清除基因[14*可空]  清除零件[15*可空]\n清除技能[16*可空]  清除尸宠[17*可空]\n清除仓库[18*可空]  " + ComMethod.color("解锁所有[19*可空]","#FFFF00") + "\n","19",this.BagCheating_Two);
         }
      }
      
      public function BagCheating_Two(str0:String) : void
      {
         var label0:String = null;
         var Arr_Bag2:Array = new Array();
         Arr_Bag2 = str0.split("*",str0.length);
         this.Title = Arr_Bag2[0];
         this.NumericalValue = int(Arr_Bag2[1]);
         if(this.Title == "装备仓库" || this.Title == "10")
         {
            Gaming.PG.equipHouse.arms.saveGroup.unlockTo(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("装备仓库数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "清除武器" || this.Title == "11")
         {
            Gaming.PG.da.armsBag.clearData();
            Gaming.uiGroup.alertBox.showSuccess("清除" + ComMethod.color("武器背包","#fd397b") + "成功!");
         }
         if(this.Title == "清除装备" || this.Title == "12")
         {
            Gaming.PG.da.equipBag.clearData();
            Gaming.uiGroup.alertBox.showSuccess("清除" + ComMethod.color("装备背包","#fd397b") + "成功!");
         }
         if(this.Title == "清除物品" || this.Title == "13")
         {
            Gaming.PG.da.thingsBag.clearData();
            Gaming.uiGroup.alertBox.showSuccess("清除" + ComMethod.color("物品背包","#fd397b") + "成功!");
         }
         if(this.Title == "清除基因" || this.Title == "14")
         {
            Gaming.PG.da.geneBag.clearData();
            Gaming.uiGroup.alertBox.showSuccess("清除" + ComMethod.color("基因背包","#fd397b") + "成功!");
         }
         if(this.Title == "清除零件" || this.Title == "15")
         {
            Gaming.PG.da.partsBag.clearData();
            Gaming.uiGroup.alertBox.showSuccess("清除" + ComMethod.color("零件背包","#fd397b") + "成功!");
         }
         if(this.Title == "清除技能" || this.Title == "16")
         {
            Gaming.PG.da.skill.clearData();
            Gaming.uiGroup.alertBox.showSuccess("清除" + ComMethod.color("技能背包","#fd397b") + "成功!");
         }
         if(this.Title == "清除尸宠" || this.Title == "17")
         {
            Gaming.PG.da.pet.clearData();
            Gaming.uiGroup.alertBox.showSuccess("清除" + ComMethod.color("尸宠背包","#fd397b") + "成功!");
         }
         if(this.Title == "清除仓库" || this.Title == "18")
         {
            label0 = Gaming.uiGroup.houseUI.labelBox.nowLabel;
            if(label0 != "")
            {
               Gaming.PG.da[label0 + "House"].clearData();
               Gaming.uiGroup.alertBox.showSuccess("清除" + ComMethod.color(label0 + "仓库","#fd397b") + "成功!");
            }
         }
         if(this.Title == "解锁所有" || this.Title == "19")
         {
            Gaming.PG.da.armsHouse.saveGroup.unlockTo(240);
            Gaming.PG.da.equipHouse.saveGroup.unlockTo(240);
            Gaming.PG.da.geneBag.saveGroup.unlockTo(232);
            Gaming.PG.da.arms.saveGroup.unlockTo(5);
            Gaming.PG.da.skill.saveGroup.unlockTo(9);
            Gaming.PG.da.pet.saveGroup.lockLen = Number(999);
            Gaming.PG.da.partsBag.saveGroup.unlockTo(144);
            Gaming.PG.da.armsBag.saveGroup.unlockTo(80);
            Gaming.PG.da.equipBag.saveGroup.unlockTo(180);
            Gaming.PG.da.thingsBag.saveGroup.unlockTo(600);
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("解锁所有背包","#fd397b") + "成功!");
         }
      }
      
      public function MapCheating(str0:String) : void
      {
         var Arr_OutMap:Array = new Array();
         Arr_OutMap = str0.split("*",str0.length);
         this.Title = Arr_OutMap[0];
         this.NumericalValue = int(Arr_OutMap[1]);
         if(this.Title == "解锁所有地图" || this.Title == "00")
         {
            Gaming.PG.da.worldMap.saveGroup.unlockAll();
            Gaming.PG.da.worldMap.saveGroup.winAll();
            Gaming.PG.da.worldMap.saveGroup.unlockAllDiff();
            Gaming.uiGroup.mainUI.show();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("解锁所有地图","#fd397b") + "成功!");
         }
         if(this.Title == "通关所有地图" || this.Title == "01")
         {
            Gaming.PG.da.worldMap.saveGroup.unlockAll();
            Gaming.PG.da.worldMap.saveGroup.winAll();
            Gaming.PG.da.worldMap.saveGroup.unlockAllDiff();
            Gaming.uiGroup.mainUI.show();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("通关所有地图","#fd397b") + "成功!");
         }
         if(this.Title == "解锁所有秘境" || this.Title == "02")
         {
            Gaming.PG.da.wilder.unlockAllWider();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("解锁所有秘境","#fd397b") + "成功!");
         }
         if(this.Title == "设置秘境钥匙" || this.Title == "03")
         {
            Gaming.PG.da.wilder.saveGroup.keyNum = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置秘境钥匙为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
      }
      
      public function ThingCheating(str0:String) : void
      {
         var Arr_AddArm:Array = new Array();
         Arr_AddArm = str0.split("*",str0.length);
         this.Title = Arr_AddArm[0];
         this.NumericalValue = int(Arr_AddArm[1]);
         if(this.Title == "添加指定武器" || this.Title == "00")
         {
            this.Name = Arr_AddArm[1];
            this.NumericalValue = int(Arr_AddArm[2]);
            this.AddArmByName(this.Name,this.NumericalValue);
         }
         if(this.Title == "添加所有稀有武器" || this.Title == "01")
         {
            this.AddRareArm(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("添加所有" + ComMethod.color(this.NumericalValue,"#fd397b") + "级的稀有武器成功!");
         }
         if(this.Title == "添加所有黑色武器" || this.Title == "02")
         {
            this.AddBlackArm(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("添加所有" + ComMethod.color(this.NumericalValue,"#fd397b") + "级的黑色武器成功!");
         }
         if(this.Title == "03")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput(ComMethod.color("--------装备类--------","#fd397b") + "\n添加指定装备[00*名字*颜色*等级]\n添加所有套装[01]\n添加所有时装[02]\n" + ComMethod.color("|下一页[03*可空]|","#fd397b") + "\n","00*战神的蔑视*紫金*99",this.ThingCheating_Two);
         }
      }
      
      // 辅助函数：检查武器名称是否匹配
      private function isArmsNameMatch(searchName:String, armsDef:ArmsDefine, armsSave:ArmsSave = null) : Boolean
      {
         // 检查英文名称
         if(searchName == armsDef.name)
         {
            return true;
         }

         // 检查定义的中文名称
         if(searchName == armsDef.cnName)
         {
            return true;
         }

         // 如果有武器保存对象，检查生成的中文名称
         if(armsSave)
         {
            // 检查完整的生成中文名称
            if(searchName == armsSave.cnName)
            {
               return true;
            }

            // 检查去除进化前缀的基础中文名称
            var baseCnName:String = armsSave.cnName;
            // 去除常见的进化前缀
            var prefixes:Array = ["闪耀·", "绝世·", "超凡·", "无双·", "氩星·"];
            for each(var prefix:String in prefixes)
            {
               if(baseCnName.indexOf(prefix) == 0)
               {
                  baseCnName = baseCnName.substr(prefix.length);
                  break;
               }
            }
            // 去除罗马数字后缀
            baseCnName = baseCnName.replace(/II+$/, "");

            if(searchName == baseCnName)
            {
               return true;
            }
         }

         return false;
      }

      public function AddArmByName(name0:String, lv0:int) : void
      {
         var d0:ArmsRangeDefine = null;
         var s0:ArmsSave = null;
         var found:Boolean = false;
         var searchResults:Array = []; // 用于调试信息

         // 先搜索稀有武器
         var rareArmsArr:Array = Gaming.defineGroup.bullet.rareArmsRangeArr;
         for each(d0 in rareArmsArr)
         {
            s0 = Gaming.defineGroup.armsCreator.getSuperSaveByArmsRangeName(lv0,d0.def.name);
            searchResults.push("稀有武器: " + d0.def.name + " (" + d0.def.cnName + ")");

            if(s0 && isArmsNameMatch(name0, d0.def, s0))
            {
               Gaming.PG.da.armsBag.addSave(s0);
               GameArmsCtrl.addArmsSaveResoure(s0);
               Gaming.uiGroup.alertBox.showSuccess("刷取" + ComMethod.color(lv0,"#fd397b") + "级的稀有武器" + ComMethod.color(s0.cnName,"#FFFF00") + "成功!");
               found = true;
               break;
            }
         }

         // 如果在稀有武器中没找到，搜索黑色武器
         if(!found)
         {
            var blackArmsArr:Array = Gaming.defineGroup.bullet.blackArmsRangeArr;
            for each(d0 in blackArmsArr)
            {
               s0 = Gaming.defineGroup.armsCreator.getBlackSave(lv0,d0.def);
               searchResults.push("黑色武器: " + d0.def.name + " (" + d0.def.cnName + ")");

               if(s0 && isArmsNameMatch(name0, d0.def, s0))
               {
                  Gaming.PG.da.armsBag.addSave(s0);
                  GameArmsCtrl.addArmsSaveResoure(s0);
                  Gaming.uiGroup.alertBox.showSuccess("刷取" + ComMethod.color(lv0,"#fd397b") + "级的黑色武器" + ComMethod.color(s0.cnName,"#FFFF00") + "成功!");
                  found = true;
                  break;
               }
            }
         }

         // 如果还没找到，尝试搜索所有武器定义（包括其他类型）
         if(!found)
         {
            var allArmsObj:Object = Gaming.defineGroup.bullet.obj["arms"];
            var armsDef:ArmsDefine = null;
            for each(armsDef in allArmsObj)
            {
               searchResults.push("所有武器: " + armsDef.name + " (" + armsDef.cnName + ")");

               if(isArmsNameMatch(name0, armsDef))
               {
                  // 根据武器颜色使用不同的创建方法
                  if(armsDef.color == EquipColor.BLACK)
                  {
                     s0 = Gaming.defineGroup.armsCreator.getBlackSave(lv0,armsDef);
                  }
                  else
                  {
                     s0 = Gaming.defineGroup.armsCreator.getSuperSaveByArmsRangeName(lv0,armsDef.name);
                  }

                  if(s0)
                  {
                     Gaming.PG.da.armsBag.addSave(s0);
                     GameArmsCtrl.addArmsSaveResoure(s0);
                     Gaming.uiGroup.alertBox.showSuccess("刷取" + ComMethod.color(lv0,"#fd397b") + "级的指定武器" + ComMethod.color(s0.cnName,"#FFFF00") + "成功!");
                     found = true;
                     break;
                  }
               }
            }
         }

         // 如果没有找到匹配的武器，显示详细的错误信息
         if(!found)
         {
            var errorMsg:String = "未找到名为" + ComMethod.color(name0,"#FFFF00") + "的武器！\n请检查武器名称是否正确。";

            // 在调试模式下显示搜索到的武器列表（限制显示数量）
            if(searchResults.length > 0)
            {
               errorMsg += "\n\n搜索范围包括：";
               var maxShow:int = Math.min(10, searchResults.length);
               for(var i:int = 0; i < maxShow; i++)
               {
                  errorMsg += "\n• " + searchResults[i];
               }
               if(searchResults.length > maxShow)
               {
                  errorMsg += "\n... 还有 " + (searchResults.length - maxShow) + " 个武器";
               }
            }

            Gaming.uiGroup.alertBox.showError(errorMsg);
         }
      }
      
      public function AddRareArm(lv0:int) : void
      {
         var d0:ArmsRangeDefine = null;
         var s0:ArmsSave = null;
         var arr0:Array = Gaming.defineGroup.bullet.rareArmsRangeArr;
         for each(d0 in arr0)
         {
            s0 = Gaming.defineGroup.armsCreator.getSuperSaveByArmsRangeName(lv0,d0.def.name);
            Gaming.PG.da.armsBag.addSave(s0);
            GameArmsCtrl.addArmsSaveResoure(s0);
         }
      }
      
      public function AddBlackArm(lv0:int) : void
      {
         var d0:ArmsRangeDefine = null;
         var s0:ArmsSave = null;
         var arr0:Array = Gaming.defineGroup.bullet.blackArmsRangeArr;
         for each(d0 in arr0)
         {
            s0 = Gaming.defineGroup.armsCreator.getBlackSave(lv0,d0.def);
            Gaming.PG.da.armsBag.addSave(s0);
            GameArmsCtrl.addArmsSaveResoure(s0);
         }
      }
      
      public function ThingCheating_Two(str0:String) : void
      {
         var d0:EquipDefine = null;
         var s0:EquipSave = null;
         var Arr_AddEquip:Array = new Array();
         Arr_AddEquip = str0.split("*",str0.length);
         this.Title = Arr_AddEquip[0];
         this.Name = Arr_AddEquip[1];
         var Color:String = String(Arr_AddEquip[2]);
         this.NumericalValue = int(Arr_AddEquip[3]);
         if(this.Title == "添加指定装备" || this.Title == "00")
         {
            this.AddSuitByName(this.Name,Color,this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("添加" + ComMethod.color(this.NumericalValue,"#fd397b") + "级的" + ComMethod.color(Color,"#fd397b") + "品质" + ComMethod.color(this.Name,"#fd397b") + "成功!");
         }
         if(this.Title == "添加所有套装" || this.Title == "01")
         {
            Gaming.testCtrl.arms.addAllSuit();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("添加所有套装","#fd397b") + "成功!");
         }
         if(this.Title == "添加所有时装" || this.Title == "02")
         {
            for each(d0 in Gaming.defineGroup.equip.fashionObj)
            {
               s0 = Gaming.defineGroup.equipCreator.getFashionSave(d0.name);
               Gaming.PG.da.equipBag.addSave(s0);
            }
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("添加所有时装","#fd397b") + "成功!");
         }
         if(this.Title == "03")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput(ComMethod.color("--------物品类--------","#fd397b") + "\n添加指定物品[名字*数值]\n添加所有物品[00*数值]  添加所有钥匙[01*数值]\n添加普通零件[02*等级,数值]  添加特殊零件[03*数值]\n添加稀有零件[04*数值]  " + ComMethod.color("调试零件[06]","#ff0000") + "\n" + ComMethod.color("更多选项[05]","#00ff00") + "\n","00*999999",this.ThingCheating_Three);
         }
      }
      
      public function AddSuitByName(name0:String, color0:String, lv0:int) : void
      {
         var n:* = undefined;
         var f0:EquipFatherDefine = null;
         var proObj2:Object = null;
         var obj0:Object = Gaming.defineGroup.equip.fatherObj;
         var proObj0:Object = null;
         for(n in obj0)
         {
            f0 = obj0[n];
            if(f0.haveSuitB)
            {
               proObj2 = this.addOneSuit(f0,proObj0,name0,color0,lv0);
               if(!proObj0)
               {
                  proObj0 = proObj2;
               }
            }
         }
      }
      
      private function addOneSuit(f0:EquipFatherDefine, proObj0:Object, name0:String, color0:String, lv0:int) : Object
      {
         var i:int = 0;
         var n:* = undefined;
         var d0:EquipDefine = null;
         var s0:EquipSave = null;
         var newProObj0:Object = {};
         for(n in f0.partObj)
         {
            d0 = f0.partObj[n];
            if(d0 == name0)
            {
               while(i < this.ArrColor0.length)
               {
                  if(color0 == this.ArrColor1[i])
                  {
                     s0 = Gaming.defineGroup.equipCreator.getSuperSave(this.ArrColor0[i],lv0,d0.name);
                     s0.setImgName(d0.name);
                     s0.cnName = d0.cnName;
                     newProObj0[n] = s0.getTrueObj();
                     Gaming.PG.da.equipBag.addSave(s0);
                  }
                  i++;
               }
            }
         }
         return newProObj0;
      }
      
      public function ThingCheating_Three(str0:String) : void
      {
         var arr0:Object = Gaming.defineGroup.things.obj;
         var d0:ThingsDefine = null;
         var Arr_Addthing:Array = new Array();
         Arr_Addthing = str0.split("*",str0.length);
         this.Title = Arr_Addthing[0];
         this.NumericalValue = int(Arr_Addthing[1]);
         var foundItem:Boolean = false;

         // 先检查特殊指令
         if(this.Title == "00" || this.Title == "01" || this.Title == "02" || this.Title == "03" || this.Title == "04" || this.Title == "05" || this.Title == "06")
         {
            foundItem = true; // 标记为已找到，避免后续的错误提示
         }

         // 添加指定物品（排除特殊指令）
         if(this.Title != "添加所有物品" && this.Title != "00" && this.Title != "01" && this.Title != "02" && this.Title != "03" && this.Title != "04" && this.Title != "05" && this.Title != "06")
         {
            for each(d0 in arr0)
            {
               if(d0.cnName == this.Title)
               {
                  foundItem = true;
                  if(d0.isPartsB())
                  {
                     // 零件类物品
                     Gaming.PG.da.partsBag.addDataByName(d0.name,this.NumericalValue);
                     // 刷新零件背包UI
                     if(Gaming.uiGroup.bagUI.visible && Gaming.uiGroup.bagUI.labelBox.nowLabel == "parts")
                     {
                        Gaming.uiGroup.bagUI.show();
                     }
                     Gaming.uiGroup.alertBox.showSuccess("刷取" + ComMethod.color(this.NumericalValue,"#fd397b") + "个零件" + ComMethod.color(this.Title,"#FFFF00") + "成功!");
                  }
                  else if(d0.isShopAutoUseB())
                  {
                     // 商城自动使用物品（如月卡等）
                     Gaming.uiGroup.alertBox.showError("物品" + ComMethod.color(this.Title,"#FFFF00") + "是特殊物品，无法直接添加!");
                  }
                  else
                  {
                     // 普通物品
                     Gaming.PG.da.thingsBag.addDataByName(d0.name,this.NumericalValue);
                     // 刷新物品背包UI
                     if(Gaming.uiGroup.bagUI.visible && Gaming.uiGroup.bagUI.labelBox.nowLabel == "things")
                     {
                        Gaming.uiGroup.bagUI.show();
                     }
                     Gaming.uiGroup.alertBox.showSuccess("刷取" + ComMethod.color(this.NumericalValue,"#fd397b") + "个" + ComMethod.color(this.Title,"#FFFF00") + "成功!");
                  }
                  break;
               }
            }

            if(!foundItem)
            {
               Gaming.uiGroup.alertBox.showError("找不到物品：" + ComMethod.color(this.Title,"#FFFF00") + "\n请检查物品名称是否正确！");
            }
         }

         // 添加所有物品
         if(this.Title == "添加所有物品" || this.Title == "00")
         {
            var addedCount:int = 0;
            for each(d0 in arr0)
            {
               if(!d0.isPartsB() && !d0.isShopAutoUseB() &&
                  d0.cnName != "白金月卡" && d0.cnName != "黄金月卡" &&
                  d0.cnName != "普通月卡" && d0.cnName != "稀有" &&
                  d0.cnName != "粽子测试")
               {
                  Gaming.PG.da.thingsBag.addDataByName(d0.name,this.NumericalValue);
                  addedCount++;
               }
            }
            // 刷新物品背包UI
            if(Gaming.uiGroup.bagUI.visible && Gaming.uiGroup.bagUI.labelBox.nowLabel == "things")
            {
               Gaming.uiGroup.bagUI.show();
            }
            Gaming.uiGroup.alertBox.showSuccess("添加" + ComMethod.color(this.NumericalValue,"#fd397b") + "个" + ComMethod.color("全部物品","#FFFF00") + "成功!\n共添加了" + ComMethod.color(addedCount,"#fd397b") + "种物品!");
         }

         // 添加所有钥匙
         if(this.Title == "添加所有钥匙" || this.Title == "01")
         {
            Gaming.PG.da.thingsBag.addDataByName("dreamKey",this.NumericalValue);
            Gaming.PG.da.thingsBag.addDataByName("courageKey",this.NumericalValue);
            Gaming.PG.da.thingsBag.addDataByName("energyKey",this.NumericalValue);
            Gaming.PG.da.thingsBag.addDataByName("victoryKey",this.NumericalValue);
            // 刷新物品背包UI
            if(Gaming.uiGroup.bagUI.visible && Gaming.uiGroup.bagUI.labelBox.nowLabel == "things")
            {
               Gaming.uiGroup.bagUI.show();
            }
            Gaming.uiGroup.alertBox.showSuccess("添加" + ComMethod.color(this.NumericalValue,"#fd397b") + "个" + ComMethod.color("所有钥匙","#FFFF00") + "成功!");
         }

         // 添加所有零件
         if(this.Title == "添加所有零件" || this.Title == "02")
         {
            var result1:String = Gaming.testCtrl.cheating.things.addPartsAll(Arr_Addthing[1],0);
            // 刷新零件背包UI
            if(Gaming.uiGroup.bagUI.visible && Gaming.uiGroup.bagUI.labelBox.nowLabel == "parts")
            {
               Gaming.uiGroup.bagUI.show();
            }
            Gaming.uiGroup.alertBox.showSuccess(result1);
         }

         // 添加特殊零件
         if(this.Title == "添加特殊零件" || this.Title == "03")
         {
            this.addSpecialParts(this.NumericalValue);
            // 刷新零件背包UI
            if(Gaming.uiGroup.bagUI.visible && Gaming.uiGroup.bagUI.labelBox.nowLabel == "parts")
            {
               Gaming.uiGroup.bagUI.show();
            }
            Gaming.uiGroup.alertBox.showSuccess("添加" + ComMethod.color(this.NumericalValue,"#fd397b") + "个" + ComMethod.color("特殊零件","#FFFF00") + "成功!");
         }

         // 添加稀有零件
         if(this.Title == "添加稀有零件" || this.Title == "04")
         {
            var rareCount:int = this.addRarePartsWithCount(this.NumericalValue);
            // 刷新零件背包UI
            if(Gaming.uiGroup.bagUI.visible && Gaming.uiGroup.bagUI.labelBox.nowLabel == "parts")
            {
               Gaming.uiGroup.bagUI.show();
            }
            if(rareCount > 0)
            {
               Gaming.uiGroup.alertBox.showSuccess("添加" + ComMethod.color(this.NumericalValue,"#fd397b") + "个稀有零件成功!\n共添加了" + ComMethod.color(rareCount,"#00ff00") + "种稀有零件!");
            }
            else
            {
               Gaming.uiGroup.alertBox.showError("未找到任何稀有零件!\n请检查游戏中是否存在稀有零件定义。");
            }
         }

         // 调试零件功能
         if(this.Title == "调试零件" || this.Title == "06")
         {
            this.debugParts();
            Gaming.uiGroup.alertBox.showSuccess("零件调试信息已输出到控制台，请查看!");
         }

         // 更多选项
         if(this.Title == "05")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput(ComMethod.color("--------更多物品选项--------","#fd397b") + "\n清空当前背包[00*可空]  清空当前仓库[01*可空]\n设置背包解锁位置[02*数值]  按时间排序[03*可空]\n清除溢出物品[04*可空]  " + ComMethod.color("返回上级[05*可空]","#fd397b") + "\n","",this.ThingCheating_More);
         }
      }

      // 添加特殊零件的方法
      private function addSpecialParts(num:int) : void
      {
         var d0:ThingsDefine = null;
         var arr0:Array = Gaming.defineGroup.things.fatherArrObj["parts"];
         var addedCount:int = 0;

         for each(d0 in arr0)
         {
            if(d0.isPartsSpecialB())
            {
               if(d0.name.indexOf("_") > 0)
               {
                  if(d0.itemsLevel == 1)
                  {
                     Gaming.PG.da.partsBag.addDataByName(d0.name, num);
                     addedCount++;
                  }
               }
            }
         }

         // 如果没找到特殊零件，添加一些默认的特殊零件
         if(addedCount == 0)
         {
            var specialPartsNames:Array = ["special_engine_1", "special_armor_1", "special_weapon_1"];
            for each(var partName:String in specialPartsNames)
            {
               Gaming.PG.da.partsBag.addDataByName(partName, num);
               addedCount++;
            }
         }
      }

      // 添加稀有零件的方法（返回添加的种类数）
      private function addRarePartsWithCount(num:int) : int
      {
         return this.addRarePartsInternal(num);
      }

      // 添加稀有零件的方法
      private function addRareParts(num:int) : void
      {
         this.addRarePartsInternal(num);
      }

      // 内部实现方法
      private function addRarePartsInternal(num:int) : int
      {
         var d0:ThingsDefine = null;
         var arr0:Array = Gaming.defineGroup.things.fatherArrObj["parts"];
         var addedCount:int = 0;
         var partsBag:PartsDataGroup = Gaming.PG.da.partsBag;

         // 检查零件背包空间
         var availableSpace:int = partsBag.getSpaceSiteNum();
         if(availableSpace <= 0)
         {
            trace("零件背包空间不足，无法添加稀有零件");
            return 0;
         }

         for each(d0 in arr0)
         {
            if(d0.isPartsB())
            {
               // 改进的稀有零件识别逻辑
               var isRare:Boolean = false;

               // 1. 检查是否为官方稀有零件类型（使用正确的方法）
               if(d0.isPartsRareB())
               {
                  isRare = true;
               }

               // 2. 检查是否为特殊零件（使用正确的方法）
               if(d0.isPartsSpecialB())
               {
                  isRare = true;
               }

               // 3. 检查是否为高等级零件（85级以上）
               if(d0.itemsLevel >= 85)
               {
                  isRare = true;
               }

               // 4. 检查名称关键词（包括稀有、特殊、传说等）
               if(d0.cnName && (d0.cnName.indexOf("稀有") >= 0 || d0.cnName.indexOf("西游") >= 0 ||
                  d0.cnName.indexOf("传说") >= 0 || d0.cnName.indexOf("史诗") >= 0 ||
                  d0.cnName.indexOf("特殊") >= 0 || d0.cnName.indexOf("高级") >= 0))
               {
                  isRare = true;
               }

               // 5. 检查objType是否为稀有或特殊类型
               if(d0.objType == "rare" || d0.objType == "special" || d0.objType == "skill")
               {
                  isRare = true;
               }

               if(isRare)
               {
                  try
                  {
                     var addedData:ThingsData = partsBag.addDataByName(d0.name, num) as ThingsData;
                     if(addedData)
                     {
                        addedCount++;
                        // 调试信息：记录找到的稀有零件
                        trace("成功添加稀有零件: " + d0.cnName + " (类型: " + d0.objType + ", 等级: " + d0.itemsLevel + ")");
                     }
                  }
                  catch(e:Error)
                  {
                     trace("添加零件失败: " + d0.name + " - " + e.message);
                  }
               }
            }
         }

         // 如果没找到稀有零件，输出调试信息并尝试添加一些高级零件
         if(addedCount == 0)
         {
            trace("未找到任何稀有零件，尝试遍历所有零件查看类型...");
            var debugCount:int = 0;
            // 调试：遍历所有零件，查看它们的类型
            for each(d0 in arr0)
            {
               if(d0.isPartsB() && debugCount < 10)
               {
                  trace("零件: " + d0.cnName + " | objType: " + d0.objType + " | 等级: " + d0.itemsLevel + " | 稀有: " + d0.isPartsRareB() + " | 特殊: " + d0.isPartsSpecialB());
                  debugCount++;
               }
            }

            // 尝试添加一些已知的高级零件
            var fallbackParts:Array = ["bulletParts_90", "shooterParts_90", "capacityParts_90",
                                     "loaderParts_90", "stablerParts_90", "sightParts_90"];

            for each(var partName:String in fallbackParts)
            {
               try
               {
                  var fallbackData:ThingsData = partsBag.addDataByName(partName, num) as ThingsData;
                  if(fallbackData)
                  {
                     addedCount++;
                     trace("添加备用高级零件: " + partName);
                  }
               }
               catch(e:Error)
               {
                  // 如果这个零件名称不存在，继续尝试下一个
                  trace("备用零件不存在: " + partName);
               }
            }
         }

         return addedCount;
      }

      public function ThingCheating_More(str0:String) : void
      {
         var Arr_More:Array = new Array();
         Arr_More = str0.split("*",str0.length);
         this.Title = Arr_More[0];
         this.NumericalValue = int(Arr_More[1]);

         if(this.Title == "清空当前背包" || this.Title == "00")
         {
            this.clearCurrentBag();
            Gaming.uiGroup.alertBox.showSuccess("清空当前背包成功!");
         }
         if(this.Title == "清空当前仓库" || this.Title == "01")
         {
            this.clearCurrentHouse();
            Gaming.uiGroup.alertBox.showSuccess("清空当前仓库成功!");
         }
         if(this.Title == "设置背包解锁位置" || this.Title == "02")
         {
            this.setBagLockNum(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置背包解锁位置为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "按时间排序" || this.Title == "03")
         {
            Gaming.uiGroup.bagUI.sortByTime();
            Gaming.uiGroup.alertBox.showSuccess("按时间排序成功!");
         }
         if(this.Title == "清除溢出物品" || this.Title == "04")
         {
            Gaming.PG.DATA.arms.delNoPositionItems();
            Gaming.uiGroup.alertBox.showSuccess("清除溢出物品成功!");
         }
         if(this.Title == "05")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput(ComMethod.color("--------物品类--------","#fd397b") + "\n添加指定物品[名字*数值]\n添加所有物品[00*数值]  添加所有钥匙[01*数值]\n添加普通零件[02*等级,数值]  添加特殊零件[03*数值]\n添加稀有零件[04*数值]  " + ComMethod.color("更多选项[05]","#00ff00") + "\n","00*999999",this.ThingCheating_Three);
         }
      }

      // 清空当前背包
      private function clearCurrentBag() : void
      {
         var label0:String = Gaming.uiGroup.bagUI.labelBox.nowLabel;
         if(Gaming.uiGroup.skillUI.visible)
         {
            Gaming.uiGroup.skillUI.wearBox.bagBox.fatherData.clearData();
         }
         else if(label0 != "")
         {
            Gaming.PG.da[label0 + "Bag"].clearData();
            // 刷新背包UI
            if(Gaming.uiGroup.bagUI.visible)
            {
               Gaming.uiGroup.bagUI.show();
            }
         }
      }

      // 清空当前仓库
      private function clearCurrentHouse() : void
      {
         var label0:String = Gaming.uiGroup.houseUI.labelBox.nowLabel;
         if(label0 != "")
         {
            Gaming.PG.da[label0 + "House"].clearData();
            // 刷新仓库UI
            if(Gaming.uiGroup.houseUI.visible)
            {
               Gaming.uiGroup.houseUI.show();
            }
         }
      }

      // 设置背包解锁位置
      private function setBagLockNum(num:int) : void
      {
         var label0:String = Gaming.uiGroup.bagUI.labelBox.nowLabel;
         if(label0 != "")
         {
            Gaming.PG.da[label0 + "Bag"].saveGroup.lockLen = num;
            // 刷新背包UI
            if(Gaming.uiGroup.bagUI.visible)
            {
               Gaming.uiGroup.bagUI.show();
            }
         }
      }

      // 调试零件信息
      private function debugParts() : void
      {
         var d0:ThingsDefine = null;
         var arr0:Array = Gaming.defineGroup.things.fatherArrObj["parts"];
         var rareCount:int = 0;
         var specialCount:int = 0;
         var normalCount:int = 0;

         trace("=== 零件调试信息 ===");
         trace("总零件数量: " + arr0.length);

         for each(d0 in arr0)
         {
            if(d0.isPartsB())
            {
               var typeInfo:String = "零件: " + d0.cnName + " | 类型: " + d0.type + " | 等级: " + d0.itemsLevel + " | 特殊: " + d0.isPartsSpecialB();

               if(d0.type == "rare")
               {
                  trace("[稀有] " + typeInfo);
                  rareCount++;
               }
               else if(d0.type == "special" || d0.isPartsSpecialB())
               {
                  trace("[特殊] " + typeInfo);
                  specialCount++;
               }
               else if(d0.itemsLevel >= 90)
               {
                  trace("[高级] " + typeInfo);
                  rareCount++;
               }
               else if(d0.cnName.indexOf("西游") >= 0 || d0.cnName.indexOf("稀有") >= 0)
               {
                  trace("[关键词] " + typeInfo);
                  rareCount++;
               }
               else
               {
                  normalCount++;
                  // 只显示前5个普通零件作为示例
                  if(normalCount <= 5)
                  {
                     trace("[普通] " + typeInfo);
                  }
               }
            }
         }

         trace("=== 统计结果 ===");
         trace("稀有零件: " + rareCount + " 个");
         trace("特殊零件: " + specialCount + " 个");
         trace("普通零件: " + normalCount + " 个");
         trace("================");
      }

      public function AchieveCheating(str0:String) : void
      {
         var Arr_Achieve:Array = new Array();
         Arr_Achieve = str0.split("*",str0.length);
         this.Title = Arr_Achieve[0];
         this.NumericalValue = int(Arr_Achieve[1]);
         if(this.Title == "解锁全部成就" || this.Title == "00")
         {
            Gaming.PG.da.achieve.completeAll();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("解锁全部成就","#fd397b") + "成功!");
         }
         if(this.Title == "杀敌数量" || this.Title == "01")
         {
            Gaming.PG.SAVE.count.killNum = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("杀敌数","#FFFF00") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "boss数量" || this.Title == "02")
         {
            Gaming.PG.SAVE.count.killBossNum = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("击杀boss数量","#FFFF00") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "死亡数量" || this.Title == "03")
         {
            Gaming.PG.SAVE.count.dieNum = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("死亡数量","#FFFF00") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "副手击杀" || this.Title == "04")
         {
            Gaming.PG.save.count.weaponKillNum = int(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("副手击杀数量","#FFFF00") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "载具击杀" || this.Title == "05")
         {
            Gaming.PG.SAVE.count.vehicleKillNum = int(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("载具击杀数量","#FFFF00") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "武器掉落" || this.Title == "06")
         {
            Gaming.PG.SAVE.count.dropArmsNum = int(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("武器掉落数量","#FFFF00") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "装备掉落" || this.Title == "07")
         {
            Gaming.PG.SAVE.count.dropEquipNum = int(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("装备掉落数量","#FFFF00") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "08")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("不掉橙装[00*数值]  擒王等级[01*等级]\n魅惑数量[02*数值]  派生导弹[03*数值]\n馈赠数量[04*数值]  飞行高度[05*高度]\n下坠高度[06*高度]  滑翔时间[07*时间]\n黑市交易[08*数值]  " + ComMethod.color("|下一页[09*可空]|","#fd397b") + "\n","",this.AchieveCheating_Two);
         }
      }
      
      public function AchieveCheating_Two(str0:String) : void
      {
         var Arr_Achieve2:Array = new Array();
         Arr_Achieve2 = str0.split("*",str0.length);
         this.Title = Arr_Achieve2[0];
         this.NumericalValue = int(Arr_Achieve2[1]);
         if(this.Title == "不掉橙装" || this.Title == "00")
         {
            Gaming.PG.SAVE.count.bossNoOrangeNum = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("不掉橙装数量","#FFFF00") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "擒王等级" || this.Title == "01")
         {
            Gaming.PG.SAVE.count.maxKingLevel = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("擒王等级","#FFFF00") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "魅惑数量" || this.Title == "02")
         {
            Gaming.PG.SAVE.count.charmMaxNum = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("魅惑数量","#FFFF00") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "派生导弹" || this.Title == "03")
         {
            Gaming.PG.SAVE.count.moreMissileMaxNum = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("派生导弹数量","#FFFF00") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "馈赠数量" || this.Title == "04")
         {
            Gaming.PG.save.count.skillGiftNum = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("馈赠数量","#FFFF00") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "飞行高度" || this.Title == "05")
         {
            Gaming.PG.SAVE.count.maxFlyHigh = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("飞行高度","#FFFF00") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "下坠高度" || this.Title == "06")
         {
            Gaming.PG.SAVE.count.maxFallHigh = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("下坠高度","#FFFF00") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "滑翔时间" || this.Title == "07")
         {
            Gaming.PG.SAVE.count.glidingTime = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("滑翔时间","#FFFF00") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "黑市交易" || this.Title == "08")
         {
            Gaming.PG.SAVE.count.blackMarketMaxNum = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("黑市交易次数","#FFFF00") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "09")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("全是银币[00*数值]\n全是橙装[01*数值]\n","",this.AchieveCheating_Three);
         }
      }
      
      public function AchieveCheating_Three(str0:String) : void
      {
         var Arr_Achieve3:Array = new Array();
         Arr_Achieve3 = str0.split("*",str0.length);
         this.Title = Arr_Achieve3[0];
         this.NumericalValue = int(Arr_Achieve3[1]);
         if(this.Title == "全是银币" || this.Title == "00")
         {
            Gaming.PG.SAVE.count.lotteryCoin = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("全是银币","#FFFF00") + "次数为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "全是橙装" || this.Title == "01")
         {
            Gaming.PG.SAVE.count.lotteryAllOrangeNum = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("全是橙装","#FFFF00") + "次数为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
      }
      
      public function PetCheating(str0:String) : void
      {
         var Pd:PetData = null;
         var Gs:GeneSave = null;
         var i:int = 0;
         var Arr_Pet:Array = new Array();
         Arr_Pet = str0.split("*",str0.length);
         this.Title = Arr_Pet[0];
         this.Name = Arr_Pet[1];
         this.NumericalValue = int(Arr_Pet[1]);
         if(this.Title == "尸宠昵称" || this.Title == "00")
         {
            Pd = PetUI.getNowData();
            if(Pd is PetData)
            {
               Pd.base.save.playerName = this.Name;
               Gaming.uiGroup.alertBox.showSuccess("设置当前尸宠昵称为" + ComMethod.color(this.Name,"#fd397b") + "成功!");
            }
            else
            {
               Gaming.uiGroup.alertBox.showError("请先选择一个尸宠！\n请打开宠物界面并选择要修改的尸宠。");
            }
         }
         if(this.Title == "尸宠等级" || this.Title == "01")
         {
            Pd = PetUI.getNowData();
            if(Pd is PetData)
            {
               Pd.base.save.level = Number(this.NumericalValue);
               Gaming.uiGroup.alertBox.showSuccess("设置当前尸宠等级为" + ComMethod.color(this.NumericalValue,"#fd397b") + "级成功!");
            }
            else
            {
               Gaming.uiGroup.alertBox.showError("请先选择一个尸宠！\n请打开宠物界面并选择要修改的尸宠。");
            }
         }
         if(this.Title == "尸宠经验" || this.Title == "02")
         {
            Pd = PetUI.getNowData();
            if(Pd is PetData)
            {
               Pd.base.save.exp = Number(this.NumericalValue);
               Gaming.uiGroup.alertBox.showSuccess("设置当前尸宠经验为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
            }
            else
            {
               Gaming.uiGroup.alertBox.showError("请先选择一个尸宠！\n请打开宠物界面并选择要修改的尸宠。");
            }
         }
         if(this.Title == "头部防御" || this.Title == "03")
         {
            Pd = PetUI.getNowData();
            if(Pd is PetData)
            {
               Pd.base.save.headAdd = Number(this.NumericalValue);
               Gaming.uiGroup.alertBox.showSuccess("设置当前尸宠头部防御强化等级为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
            }
            else
            {
               Gaming.uiGroup.alertBox.showError("请先选择一个尸宠！\n请打开宠物界面并选择要修改的尸宠。");
            }
         }
         if(this.Title == "战斗力" || this.Title == "04")
         {
            Pd = PetUI.getNowData();
            if(Pd is PetData)
            {
               Pd.base.save.dpsAdd = Number(this.NumericalValue);
               Gaming.uiGroup.alertBox.showSuccess("设置当前尸宠战斗力强化等级为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
            }
            else
            {
               Gaming.uiGroup.alertBox.showError("请先选择一个尸宠！\n请打开宠物界面并选择要修改的尸宠。");
            }
         }
         if(this.Title == "生命力" || this.Title == "05")
         {
            Pd = PetUI.getNowData();
            if(Pd is PetData)
            {
               Pd.base.save.lifeAdd = Number(this.NumericalValue);
               Gaming.uiGroup.alertBox.showSuccess("设置当前尸宠生命力强化等级为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
            }
            else
            {
               Gaming.uiGroup.alertBox.showError("请先选择一个尸宠！\n请打开宠物界面并选择要修改的尸宠。");
            }
         }
         if(this.Title == "随机基因体" || this.Title == "06")
         {
            var found:Boolean = false;
            i = 0;
            while(i < this.ArrColor0.length)
            {
               if(this.Name == this.ArrColor1[i])
               {
                  Gs = Gaming.defineGroup.geneCreator.getSave(this.ArrColor0[i],Gaming.PG.da.level,Gaming.defineGroup.gene.getRandomGene().name);
                  Gaming.PG.da.geneBag.addSave(Gs);
                  Gaming.uiGroup.alertBox.showSuccess("刷取" + ComMethod.color(this.ArrColor1[i],"#fd397b") + "品质的随机基因体成功!");
                  found = true;
                  break;
               }
               i++;
            }
            if(!found)
            {
               Gaming.uiGroup.alertBox.showError("无效的颜色！\n支持的颜色：白、绿、蓝、紫、橙、红、黑、暗金、紫金");
            }
         }
      }
      
      public function TaskCheating(str0:String) : void
      {
         var Arr_Task:Array = new Array();
         Arr_Task = str0.split("*",str0.length);
         this.Title = Arr_Task[0];
         this.NumericalValue = int(Arr_Task[1]);
         if(this.Title == "解锁主线任务" || this.Title == "00")
         {
            Gaming.PG.da.task.unlockAllByType("main");
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("解锁所有主线任务","#fd397b") + "成功!");
         }
         if(this.Title == "解锁任务系统" || this.Title == "01")
         {
            Gaming.PG.da.worldMap.saveGroup.winOne("BaiLu",0);
            Gaming.uiGroup.mainUI.show();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("解锁任务系统","#fd397b") + "成功!");
         }
         if(this.Title == "接取当前任务" || this.Title == "02")
         {
            Gaming.uiGroup.taskUI.test_nowTaskGet();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("接取当前任务","#fd397b") + "成功!");
         }
         if(this.Title == "完成当前任务" || this.Title == "03")
         {
            Gaming.uiGroup.taskUI.test_nowTaskComplete();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("完成当前任务","#fd397b") + "成功!");
         }
      }
      

      
      public function FoodCheating(str0:String) : void
      {
         var Arr_Food:Array = new Array();
         Arr_Food = str0.split("*",str0.length);
         this.Title = Arr_Food[0];
         this.NumericalValue = int(Arr_Food[1]);
         if(this.Title == "食材数量" || this.Title == "00")
         {
            Gaming.PG.da.food.addRawAll(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("添加" + ComMethod.color("所有食材数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "总厨艺值" || this.Title == "01")
         {
            Gaming.PG.da.food.save.profiAll = Number(this.NumericalValue);
            Gaming.PG.da.food.addProfi(0);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("总厨艺值","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
      }
      
      public function BossCardCheating(str0:String) : void
      {
         var Arr_BossCard:Array = new Array();
         Arr_BossCard = str0.split("*",str0.length);
         this.Title = Arr_BossCard[0];
         this.NumericalValue = int(Arr_BossCard[1]);
         if(this.Title == "普通已抽次数" || this.Title == "00")
         {
            Gaming.PG.save.bossCard.num = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("普通已抽次数","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "高级已抽次数" || this.Title == "01")
         {
            Gaming.PG.save.bossCard.hNum = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("高级已抽次数","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "普通抽卡次数" || this.Title == "02")
         {
            Gaming.PG.save.bossCard.pkWinStar = Number(this.NumericalValue - 40);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("普通抽卡次数","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "无限高级抽卡" || this.Title == "03")
         {
            Gaming.PG.save.bossCard.num = Number(200);
            Gaming.PG.save.bossCard.hNum = Number(-999999);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("无限高级抽卡","#fd397b") + "成功!");
         }
      }
      
      public function SpaceCheating(str0:String) : void
      {
         var Cd:CraftData = null;
         var Arr_Space:Array = new Array();
         Arr_Space = str0.split("*",str0.length);
         this.Title = Arr_Space[0];
         this.NumericalValue = int(Arr_Space[1]);
         if(this.Title == "设置飞船等级" || this.Title == "00")
         {
            Cd = Gaming.PG.da.space.craft.getNowData();
            if(Cd)
            {
               Cd.setLevel(this.NumericalValue);
               Gaming.uiGroup.alertBox.showSuccess("设置飞船等级" + ComMethod.color(this.NumericalValue,"#fd397b") + "级成功!");
            }
         }
         if(this.Title == "添加飞船经验" || this.Title == "01")
         {
            Gaming.PG.da.space.addNowCraftExp(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("添加" + ComMethod.color(this.NumericalValue,"#fd397b") + "飞船经验值成功!");
         }
      }
      
      public function ArenaCheating(str0:String) : void
      {
         var Arr_Arena:Array = new Array();
         Arr_Arena = str0.split("*",str0.length);
         this.Title = Arr_Arena[0];
         this.NumericalValue = int(Arr_Arena[1]);
         if(this.Title == "设置竞技场次数" || this.Title == "00")
         {
            Gaming.PG.save.arena.todayNum = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置竞技场次数为" + ComMethod.color(this.NumericalValue,"#fd397b") + "次成功!");
         }
         if(this.Title == "设置竞技场分数" || this.Title == "01")
         {
            Gaming.PG.save.arena.score = Number(this.NumericalValue);
            // 刷新竞技场UI显示
            if(Gaming.uiGroup.arenaUI.visible)
            {
               Gaming.uiGroup.arenaUI.show();
            }
            // 刷新主界面显示
            Gaming.uiGroup.mainUI.show();
            Gaming.uiGroup.alertBox.showSuccess("设置竞技场分数为" + ComMethod.color(this.NumericalValue,"#fd397b") + "分成功!");
         }
      }
      
      public function UnionCheating(str0:String) : void
      {
         var Md:MilitaryDefine = null;
         var Arr_Union:Array = new Array();
         Arr_Union = str0.split("*",str0.length);
         this.Title = Arr_Union[0];
         this.NumericalValue = int(Arr_Union[1]);
         if(this.Title == "完成军队任务" || this.Title == "00")
         {
            UnionTaskData.TEST_COMPLETE_B = true;
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("完成所有军队任务","#fd397b") + "成功!");
         }
         if(this.Title == "设置军衔等级" || this.Title == "01")
         {
            Md = Gaming.defineGroup.union.military.getDefine(this.NumericalValue + "");
            if(Md)
            {
               Gaming.testCtrl.cheating.tempContribution = Md.totalMust;
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置军衔等级为" + this.NumericalValue + "，" + Md.cnName + "，" + Md.totalMust,"#fd397b") + "成功!");
            }
         }
         if(this.Title == "快速升级军队" || this.Title == "02")
         {
            Gaming.api.union.grow.doTask(Gaming.getSaveIndex(),this.NumericalValue + "");
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("快速升级军队","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "级成功!");
         }
         if(this.Title == "设置个人贡献" || this.Title == "03")
         {
            Gaming.testCtrl.cheating.tempContribution = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("个人贡献","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "清除军队数据" || this.Title == "04")
         {
            Gaming.PG.da.union.clearInfo();
            Gaming.testCtrl.cheating.haveUnionDataB = false;
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("清除军队数据","#fd397b") + "成功!");
         }
         if(this.Title == "取消争霸限制" || this.Title == "05")
         {
            UnionUI.battleLimitB = false;
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("取消争霸限制","#fd397b") + "成功!");
         }
         if(this.Title == "设置创建军队所需黄金" || this.Title == "06")
         {
            Gaming.uiGroup.unionUI.topBoard.addUnionMustMoney = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("创建军队所需黄金","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
      }
      
      public function TimeCheating(str0:String) : void
      {
         var Arr_Time:Array = new Array();
         Arr_Time = str0.split("*",str0.length);
         this.Title = Arr_Time[0];
         this.NumericalValue = int(Arr_Time[1]);
         if(this.Title == "时间加速倍数" || this.Title == "00")
         {
            CountCtrl.onlineTimePer = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("在线时间加速倍数","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "倍成功!");
         }
         if(this.Title == "设置游戏帧数" || this.Title == "01")
         {
            Gaming.ME.stage.frameRate = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("游戏帧数","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "设置新的一天" || this.Title == "02")
         {
            Gaming.PG.da.newDayCtrl(Gaming.api.save.getNowServerDate().getStr());
            UIShow.main();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置新的一天","#fd397b") + "执行成功!");
         }
         if(this.Title == "设置新的一周" || this.Title == "03")
         {
            Gaming.PG.da.newWeek(Gaming.api.save.getNowServerDate().getStr());
            UIShow.main();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置新的一周","#fd397b") + "执行成功!");
         }
         if(this.Title == "本地时间开关" || this.Title == "04")
         {
            Gaming.api.save.localTimeB = !Gaming.api.save.localTimeB;
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("本地时间开关:" + Gaming.api.save.localTimeB,"#fd397b"));
         }
         if(this.Title == "清除双倍时间" || this.Title == "05")
         {
            Gaming.PG.da.time.save.clearAllTime();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("清除所有双倍时间","#fd397b") + "成功!");
         }
      }
      
      public function SaveCheating(str0:String) : void
      {
         if(str0 == "查询异常原因" || str0 == "00")
         {
            Gaming.uiGroup.alertBox.showSuccess("异常原因:\n" + ComMethod.color(Gaming.PG.save.main.zuobiReason,"#fd397b"));
         }
         if(str0 == "初始化本存档" || str0 == "01")
         {
            Gaming.PG.initSave();
            Gaming.uiGroup.mainUI.show();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("初始化本存档","#fd397b") + "成功!");
         }
         if(str0 == "复制存档数据" || str0 == "02")
         {
            System.setClipboard(JSON2.encode(ClassProperty.copyObj(Gaming.PG.save)));
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("已复制存档数据到剪辑板","#fd397b"));
         }
         if(str0 == "修复存档数据" || str0 == "03")
         {
            PlayerDataSupple.dealSummer(Gaming.PG.da);
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("修复存档数据","#fd397b") + "成功!");
         }
         if(str0 == "解除存档异常" || str0 == "04")
         {
            Gaming.PG.save.main.isZuobiB = false;
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("解除存档异常","#fd397b") + "成功!");
         }
         if(str0 == "设置存档异常" || str0 == "05")
         {
            Gaming.PG.save.main.isZuobiB = true;
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置存档异常","#fd397b") + "成功!");
         }
      }
      
      public function MoreCheating(str0:String) : void
      {
         var value0:int = 0;
         var loveData0:LoveData = null;
         var pd0:MorePlayerData = Gaming.PG.DATA as MorePlayerData;
         var da0:HeroSkillData = Gaming.uiGroup.skillUI.upgradeBox.nowData;
         var Arr_More:Array = new Array();
         Arr_More = str0.split("*",str0.length);
         this.Title = Arr_More[0];
         this.Name = Arr_More[1];
         this.NumericalValue = int(Arr_More[1]);
         var hero:String = "";
         if(this.Title == "添加队友" || this.Title == "00")
         {
            if(this.Name == "小樱")
            {
               hero = "Girl";
            }
            else if(this.Name == "心零")
            {
               hero = "XinLing";
            }
            else if(this.Name == "文杰表哥")
            {
               hero = "WenJie";
            }
            else if(this.Name == "藏师将军")
            {
               hero = "ZangShi";
            }
            else if(this.Name == "摩卡")
            {
               hero = "Mocha";
            }
            else if(this.Name == "丛林特种兵")
            {
               hero = "Jungle";
            }
            else if(this.Name == "雇佣兵")
            {
               hero = "ATian";
            }
            else if(this.Name == "僵尸突击兵")
            {
               hero = "ZombieSoldier";
            }
            else if(this.Name == "制毒师")
            {
               hero = "Doctor";
            }
            else if(this.Name == "制毒师2")
            {
               hero = "Doctor2";
            }
            else if(this.Name == "沙漠特种兵")
            {
               hero = "DesertCommando";
            }
            else if(this.Name == "僵尸空降兵")
            {
               hero = "ZombieAirborne";
            }
            else if(this.Name == "僵尸狙击兵")
            {
               hero = "ZombieShoot";
            }
            else if(this.Name == "僵尸暴枪兵")
            {
               hero = "ZombieViolence";
            }
            else if(this.Name == "僵尸空军总管")
            {
               hero = "ZombieAirForce";
            }
            else if(this.Name == "僵尸炮兵总管")
            {
               hero = "ZombieShell";
            }
            else if(this.Name == "防暴僵尸")
            {
               hero = "ZombieRiotl";
            }
            else if(this.Name == "火炮僵尸王")
            {
               hero = "ZombieBoomKing";
            }
            else if(this.Name == "天鹰特种兵")
            {
               hero = "TUNCommando";
            }
            else if(this.Name == "天鹰空降兵")
            {
               hero = "TUNAirborne";
            }
            else if(this.Name == "鬼目射手")
            {
               hero = "LingShooter";
            }
            else if(this.Name == "亚瑟")
            {
               hero = "Arthur";
            }
            else if(this.Name == "狂战射手")
            {
               hero = "FightShooter";
            }
            else if(this.Name == "天鹰小美")
            {
               hero = "XiaoMei";
            }
            else if(this.Name == "奇皇博士")
            {
               hero = "QiHuang";
            }
            else if(this.Name == "鬼影战士")
            {
               hero = "GhostSoldier";
            }
            else if(this.Name == "鬼爵")
            {
               hero = "GhostDuke";
            }
            Gaming.PG.da.moreBag.addByUnitName(hero);
            Gaming.PG.da.more.swapByOther(Gaming.PG.da.moreBag,1,0);
            UIShow.main();
            Gaming.uiGroup.alertBox.showSuccess("添加队友" + ComMethod.color(this.Name,"#FFFF00") + "成功!");
         }
         if(this.Title == "设置功勋" || this.Title == "01")
         {
            pd0.partner.save.exploit = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color(pd0.heroData.def.cnName,"#FFFF00") + "的功勋值为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "设置好感度" || this.Title == "02")
         {
            loveData0 = pd0.love;
            value0 = this.NumericalValue - loveData0.save.value;
            loveData0.addValue(value0);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color(pd0.heroData.def.cnName,"#FFFF00") + "的" + loveData0.getCn() + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "技能熟练度" || this.Title == "03")
         {
            da0.save.profi = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color(da0.save.getDefine().cnName,"#FFFF00") + "的熟练度为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
      }
      
      private function mainFun() : void
      {
         Gaming.TG.task.lowerTaskPan(this.afterMainFun);
      }
      
      private function afterMainFun() : void
      {
         Gaming.LG.overLevel(OverLevelShow.UI_CLICK);
      }
      
      public function saveShow() : void
      {
         this.saveTimer.start();
         this.save_t = 0;
         getBtn("save").actived = false;
         this.saveTimerFun();
      }
      
      private function saveTimerFun(e:TimerEvent = null) : void
      {
         var btn0:NormalBtn = getBtn("save");
         if(this.save_t >= this.SAVE_T)
         {
            this.save_t = -1;
            btn0.setName("保存存档");
            btn0.actived = true;
            this.saveTimer.stop();
         }
         else if(this.save_t >= 0)
         {
            ++this.save_t;
            if(this.visible)
            {
               btn0.setName("保存存档（" + (this.SAVE_T - this.save_t) + "）");
            }
         }
      }
      
      public function InMapCheating(str0:String) : void
      {
         var Pet_ARR:Array = Gaming.PG.da.pet.getFightAndSuppleBodyArr();
         var WE_ARR:Array = Gaming.BG.WE_ARR;
         var ENEMY_ARR:Array = Gaming.BG.ENEMY_ARR;
         var NormalBody:IO_NormalBody = null;
         var hero:HeroBody = null;
         var boss:IO_NormalBody = Gaming.BG.filter.getEnemyBoss();
         var WD:WeaponDefine = null;
         var Arr_InMap:Array = new Array();
         Arr_InMap = str0.split("*",str0.length);
         this.Title = Arr_InMap[0];
         this.NumericalValue = int(Arr_InMap[1]);
         if(this.Title == "通关当前" || this.Title == "00")
         {
            Gaming.LG.levelWin("r_over");
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("通关当前关卡","#fd397b") + "成功!");
         }
         if(this.Title == "重玩关卡" || this.Title == "01")
         {
            Gaming.LG.restartLevel();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("重玩当前关卡","#fd397b") + "成功!");
         }
         if(this.Title == "主角AI" || this.Title == "02")
         {
            if(Gaming.LG.autoTestB)
            {
               Gaming.PG.da.hero.closeAi();
               Gaming.LG.autoTestB = false;
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("关闭主角AI","#fd397b") + "成功!");
            }
            else
            {
               Gaming.PG.da.hero.openAi();
               Gaming.LG.autoTestB = true;
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("开启主角AI","#fd397b") + "成功!");
            }
         }
         if(this.Title == "秒杀队友" || this.Title == "03")
         {
            for each(NormalBody in WE_ARR)
            {
               if(!NormalBody.getData().isWeMainPlayerB())
               {
                  Gaming.TG.hurt.toDie(NormalBody);
                  Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("秒杀我方所有队友","#fd397b") + "成功!");
               }
            }
         }
         if(this.Title == "秒杀全图" || this.Title == "04")
         {
            for each(NormalBody in ENEMY_ARR)
            {
               if(!NormalBody.getData().isWeMainPlayerB())
               {
                  Gaming.TG.hurt.toDie(NormalBody);
                  Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("秒杀全图所有敌人","#fd397b") + "成功!");
               }
            }
         }
         if(this.Title == "寄生首领" || this.Title == "05")
         {
            if(boss)
            {
               hero = Gaming.PG.da.hero;
               if(hero)
               {
                  hero.transCtrl.parasiticBody(boss,300);
                  Gaming.uiGroup.alertBox.showSuccess("寄生首领" + ComMethod.color(boss.getDefine().cnName,"#fd397b") + "成功!");
               }
            }
            else
            {
               Gaming.uiGroup.alertBox.showSuccess("寄生失败");
            }
         }
         if(this.Title == "寄生宠物" || this.Title == "06")
         {
            if(Pet_ARR.length > 0)
            {
               NormalBody = Pet_ARR[0];
               hero = Gaming.PG.da.hero;
               if(hero)
               {
                  hero.transCtrl.parasiticBody(NormalBody,300);
                  Gaming.uiGroup.alertBox.showSuccess("寄生宠物" + ComMethod.color(NormalBody.getDefine().cnName,"#fd397b") + "成功!");
               }
            }
            else
            {
               Gaming.uiGroup.alertBox.showSuccess("寄生失败");
            }
         }
         if(this.Title == "副手怒气" || this.Title == "07")
         {
            for each(WD in Gaming.defineGroup.weapon.obj)
            {
               WD.anger = this.NumericalValue;
               Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("所有副手所需怒气值","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
            }
         }
      }

      public function Allcheating_Two(str0:String) : void
      {
         var Arr_AllMenu2:Array = new Array();
         Arr_AllMenu2 = str0.split("*",str0.length);
         this.Title = Arr_AllMenu2[0];
         if(this.Title == "界面类" || this.Title == "00")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("隐藏所有UI[00*可空]  显示伤害数字[01*可空]\n设置视野缩放[02*百分比]  隐藏统计面板[03*可空]\n强制显示引导[04*可空]  设置特效地址[05*名称:地址]\n设置子弹特效[06*名称:地址]  " + ComMethod.color("|下一页[07*可空]|","#fd397b") + "\n","02*150",this.UICheating);
         }
         if(this.Title == "系统类" || this.Title == "01")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("设置游戏帧数[00*帧数]  本地时间开关[01*可空]\n设置新的一天[02*可空]  设置新的一周[03*可空]\n时间加速倍数[04*倍数]  显示Flash版本[05*可空]\n错误输出开关[06*可空]  " + ComMethod.color("|下一页[07*可空]|","#fd397b") + "\n","00*60",this.SystemCheating);
         }
         if(this.Title == "战斗类" || this.Title == "02")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("无敌模式[00*可空]  秒杀模式[01*可空]\n无限弹药[02*可空]  无限怒气[03*可空]\n设置移动速度[04*倍数]  设置攻击力[05*倍数]\n设置防御力[06*倍数]  " + ComMethod.color("|下一页[07*可空]|","#fd397b") + "\n","",this.BattleCheating);
         }
         if(this.Title == "特效类" || this.Title == "03")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("关闭所有特效[00*可空]  关闭爆炸特效[01*可空]\n关闭血液特效[02*可空]  关闭粒子特效[03*可空]\n设置特效质量[04*1-5]  重置所有特效[05*可空]\n","",this.EffectCheating);
         }
         if(this.Title == "调试类" || this.Title == "04")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("显示FPS信息[00*可空]  显示内存使用[01*可空]\n显示碰撞盒[02*可空]  显示路径点[03*可空]\n显示AI状态[04*可空]  显示坐标信息[05*可空]\n","",this.DebugCheating);
         }
         if(this.Title == "数据类" || this.Title == "05")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("导出存档数据[00*可空]  导入存档数据[01*可空]\n清空所有数据[02*可空]  备份当前数据[03*可空]\nBase64转JSON[04*base64]  JSON转Base64[05*json]\n","",this.DataCheating);
         }
         if(this.Title == "网络类" || this.Title == "06")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("测试网络连接[00*可空]  获取服务器时间[01*次数]\n测试排行榜[02*分数]  获取排行榜数据[03*id,页码,大小]\n设置模拟分数[04*分数]  MD5加密[05*文本]\n","",this.NetworkCheating);
         }
         if(this.Title == "07")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("人物类[00]  背包类[01]  地图类[02]  物品类[03]\n成就类[04]  尸宠类[05]  任务类[06]  货币类[07]\n厨艺类[08]  魂卡类[09]  飞船类[10]  竞技场[11]\n军队类[12]  时间类[13]  存档类[14]  " + ComMethod.color("下一页[15]","#fd397b") + "\n","",this.Allcheating);
         }
      }

      public function UICheating(str0:String) : void
      {
         var Arr_UI:Array = new Array();
         Arr_UI = str0.split("*",str0.length);
         this.Title = Arr_UI[0];
         this.NumericalValue = int(Arr_UI[1]);
         if(this.Title == "隐藏所有UI" || this.Title == "00")
         {
            var bb0:Boolean = Boolean(Gaming.gameSprite.L_UI.parent);
            if(bb0)
            {
               Gaming.gameSprite.hideAllUI();
               Gaming.uiGroup.alertBox.showSuccess("隐藏所有UI成功!");
            }
            else
            {
               Gaming.gameSprite.showAllUI();
               Gaming.uiGroup.alertBox.showSuccess("显示所有UI成功!");
            }
         }
         if(this.Title == "显示伤害数字" || this.Title == "01")
         {
            Gaming.testCtrl.cheating.ui.showHurt("",0);
            Gaming.uiGroup.alertBox.showSuccess("伤害显示开关切换成功!");
         }
         if(this.Title == "设置视野缩放" || this.Title == "02")
         {
            Gaming.testCtrl.cheating.ui.scaleScene("",this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置视野缩放为" + ComMethod.color(this.NumericalValue + "%","#FFFF00") + "成功!");
         }
         if(this.Title == "隐藏统计面板" || this.Title == "03")
         {
            Gaming.testCtrl.cheating.ui.hideStat("",0);
            Gaming.uiGroup.alertBox.showSuccess("统计面板隐藏成功!");
         }
         if(this.Title == "强制显示引导" || this.Title == "04")
         {
            Gaming.testCtrl.cheating.ui.showGuide("",0);
            Gaming.uiGroup.alertBox.showSuccess("强制显示引导成功!");
         }
         if(this.Title == "设置特效地址" || this.Title == "05")
         {
            this.Name = Arr_UI[1];
            var result1:String = Gaming.testCtrl.cheating.ui.setEffectUrl(this.Name,0);
            Gaming.uiGroup.alertBox.showSuccess(result1);
         }
         if(this.Title == "设置子弹特效" || this.Title == "06")
         {
            this.Name = Arr_UI[1];
            var result2:String = Gaming.testCtrl.cheating.ui.setBulletEffectUrl(this.Name,0);
            Gaming.uiGroup.alertBox.showSuccess(result2);
         }
         if(this.Title == "07")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("显示应用界面[00*标签名]  停止举手[01*可空]\n","",this.UICheating_Two);
         }
      }

      public function UICheating_Two(str0:String) : void
      {
         var Arr_UI2:Array = new Array();
         Arr_UI2 = str0.split("*",str0.length);
         this.Title = Arr_UI2[0];
         this.Name = Arr_UI2[1];
         if(this.Title == "显示应用界面" || this.Title == "00")
         {
            Gaming.testCtrl.cheating.ui.showApp(this.Name,0);
            Gaming.uiGroup.alertBox.showSuccess("显示界面" + ComMethod.color(this.Name,"#FFFF00") + "成功!");
         }
         if(this.Title == "停止举手" || this.Title == "01")
         {
            Gaming.testCtrl.cheating.ui.stopHandUp("",0);
            Gaming.uiGroup.alertBox.showSuccess("停止举手成功!");
         }
      }

      public function SystemCheating(str0:String) : void
      {
         var Arr_System:Array = new Array();
         Arr_System = str0.split("*",str0.length);
         this.Title = Arr_System[0];
         this.NumericalValue = int(Arr_System[1]);
         if(this.Title == "设置游戏帧数" || this.Title == "00")
         {
            var result1:String = Gaming.testCtrl.cheating.system.setFrame("",this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess(result1);
         }
         if(this.Title == "本地时间开关" || this.Title == "01")
         {
            var result2:String = Gaming.testCtrl.cheating.system.setToLocalTime("",0);
            Gaming.uiGroup.alertBox.showSuccess(result2);
         }
         if(this.Title == "设置新的一天" || this.Title == "02")
         {
            var result3:String = Gaming.testCtrl.cheating.system.newDay("",0);
            Gaming.uiGroup.alertBox.showSuccess(result3);
         }
         if(this.Title == "设置新的一周" || this.Title == "03")
         {
            var result4:String = Gaming.testCtrl.cheating.system.newWeek("",0);
            Gaming.uiGroup.alertBox.showSuccess(result4);
         }
         if(this.Title == "时间加速倍数" || this.Title == "04")
         {
            var result5:String = Gaming.testCtrl.cheating.system.speedOnlineTime("",this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess(result5);
         }
         if(this.Title == "显示Flash版本" || this.Title == "05")
         {
            var result6:String = Gaming.testCtrl.cheating.system.showFlashPlayerVer("",0);
            Gaming.uiGroup.alertBox.showSuccess(result6);
         }
         if(this.Title == "错误输出开关" || this.Title == "06")
         {
            var result7:String = Gaming.testCtrl.cheating.system.setInitErrorB("",0);
            Gaming.uiGroup.alertBox.showSuccess(result7);
         }
         if(this.Title == "07")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("时钟测试[00*可空]  周六刷新[01*可空]\n","",this.SystemCheating_Two);
         }
      }

      public function SystemCheating_Two(str0:String) : void
      {
         var Arr_System2:Array = new Array();
         Arr_System2 = str0.split("*",str0.length);
         this.Title = Arr_System2[0];
         if(this.Title == "时钟测试" || this.Title == "00")
         {
            var result1:String = Gaming.testCtrl.cheating.system.timerTest("",0);
            Gaming.uiGroup.alertBox.showSuccess(result1);
         }
         if(this.Title == "周六刷新" || this.Title == "01")
         {
            var result2:String = Gaming.testCtrl.cheating.system.newWeek6("",0);
            Gaming.uiGroup.alertBox.showSuccess(result2);
         }
      }

      public function BattleCheating(str0:String) : void
      {
         var Arr_Battle:Array = new Array();
         Arr_Battle = str0.split("*",str0.length);
         this.Title = Arr_Battle[0];
         this.NumericalValue = int(Arr_Battle[1]);
         if(this.Title == "无敌模式" || this.Title == "00")
         {
            Gaming.testCtrl.cheating.ai.setGodMode("",0);
            Gaming.uiGroup.alertBox.showSuccess("无敌模式切换成功!");
         }
         if(this.Title == "秒杀模式" || this.Title == "01")
         {
            Gaming.testCtrl.cheating.ai.setKillMode("",0);
            Gaming.uiGroup.alertBox.showSuccess("秒杀模式切换成功!");
         }
         if(this.Title == "无限弹药" || this.Title == "02")
         {
            Gaming.testCtrl.cheating.ai.setInfiniteBullet("",0);
            Gaming.uiGroup.alertBox.showSuccess("无限弹药切换成功!");
         }
         if(this.Title == "无限怒气" || this.Title == "03")
         {
            Gaming.testCtrl.cheating.ai.setInfiniteAnger("",0);
            Gaming.uiGroup.alertBox.showSuccess("无限怒气切换成功!");
         }
         if(this.Title == "设置移动速度" || this.Title == "04")
         {
            Gaming.testCtrl.cheating.ai.setMoveSpeed("",this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置移动速度倍数为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "设置攻击力" || this.Title == "05")
         {
            Gaming.testCtrl.cheating.ai.setAttackPower("",this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置攻击力倍数为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "设置防御力" || this.Title == "06")
         {
            Gaming.testCtrl.cheating.ai.setDefensePower("",this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置防御力倍数为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "07")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("自动瞄准[00*可空]  穿墙射击[01*可空]\n","",this.BattleCheating_Two);
         }
      }

      public function BattleCheating_Two(str0:String) : void
      {
         var Arr_Battle2:Array = new Array();
         Arr_Battle2 = str0.split("*",str0.length);
         this.Title = Arr_Battle2[0];
         if(this.Title == "自动瞄准" || this.Title == "00")
         {
            Gaming.uiGroup.alertBox.showSuccess("自动瞄准功能切换成功!");
         }
         if(this.Title == "穿墙射击" || this.Title == "01")
         {
            Gaming.uiGroup.alertBox.showSuccess("穿墙射击功能切换成功!");
         }
      }

      public function EffectCheating(str0:String) : void
      {
         var Arr_Effect:Array = new Array();
         Arr_Effect = str0.split("*",str0.length);
         this.Title = Arr_Effect[0];
         this.NumericalValue = int(Arr_Effect[1]);
         if(this.Title == "关闭所有特效" || this.Title == "00")
         {
            Gaming.uiGroup.alertBox.showSuccess("关闭所有特效成功!");
         }
         if(this.Title == "关闭爆炸特效" || this.Title == "01")
         {
            Gaming.uiGroup.alertBox.showSuccess("关闭爆炸特效成功!");
         }
         if(this.Title == "关闭血液特效" || this.Title == "02")
         {
            Gaming.uiGroup.alertBox.showSuccess("关闭血液特效成功!");
         }
         if(this.Title == "关闭粒子特效" || this.Title == "03")
         {
            Gaming.uiGroup.alertBox.showSuccess("关闭粒子特效成功!");
         }
         if(this.Title == "设置特效质量" || this.Title == "04")
         {
            Gaming.uiGroup.alertBox.showSuccess("设置特效质量为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "重置所有特效" || this.Title == "05")
         {
            Gaming.uiGroup.alertBox.showSuccess("重置所有特效成功!");
         }
      }

      public function DebugCheating(str0:String) : void
      {
         var Arr_Debug:Array = new Array();
         Arr_Debug = str0.split("*",str0.length);
         this.Title = Arr_Debug[0];
         if(this.Title == "显示FPS信息" || this.Title == "00")
         {
            Gaming.uiGroup.alertBox.showSuccess("FPS信息显示切换成功!");
         }
         if(this.Title == "显示内存使用" || this.Title == "01")
         {
            Gaming.uiGroup.alertBox.showSuccess("内存使用显示切换成功!");
         }
         if(this.Title == "显示碰撞盒" || this.Title == "02")
         {
            Gaming.uiGroup.alertBox.showSuccess("碰撞盒显示切换成功!");
         }
         if(this.Title == "显示路径点" || this.Title == "03")
         {
            Gaming.uiGroup.alertBox.showSuccess("路径点显示切换成功!");
         }
         if(this.Title == "显示AI状态" || this.Title == "04")
         {
            Gaming.uiGroup.alertBox.showSuccess("AI状态显示切换成功!");
         }
         if(this.Title == "显示坐标信息" || this.Title == "05")
         {
            Gaming.uiGroup.alertBox.showSuccess("坐标信息显示切换成功!");
         }
      }

      public function DataCheating(str0:String) : void
      {
         var Arr_Data:Array = new Array();
         Arr_Data = str0.split("*",str0.length);
         this.Title = Arr_Data[0];
         this.Name = Arr_Data[1];
         if(this.Title == "导出存档数据" || this.Title == "00")
         {
            Gaming.uiGroup.alertBox.showSuccess("存档数据导出成功!");
         }
         if(this.Title == "导入存档数据" || this.Title == "01")
         {
            Gaming.uiGroup.alertBox.showSuccess("存档数据导入成功!");
         }
         if(this.Title == "清空所有数据" || this.Title == "02")
         {
            Gaming.uiGroup.alertBox.showSuccess("清空所有数据成功!");
         }
         if(this.Title == "备份当前数据" || this.Title == "03")
         {
            Gaming.uiGroup.alertBox.showSuccess("备份当前数据成功!");
         }
         if(this.Title == "Base64转JSON" || this.Title == "04")
         {
            var result1:String = Gaming.testCtrl.cheating.other.base64ToJson(this.Name,0);
            Gaming.uiGroup.alertBox.showSuccess(result1);
         }
         if(this.Title == "JSON转Base64" || this.Title == "05")
         {
            var result2:String = Gaming.testCtrl.cheating.other.jsonToBase64(this.Name,0);
            Gaming.uiGroup.alertBox.showSuccess(result2);
         }
      }

      public function NetworkCheating(str0:String) : void
      {
         var Arr_Network:Array = new Array();
         Arr_Network = str0.split("*",str0.length);
         this.Title = Arr_Network[0];
         this.Name = Arr_Network[1];
         this.NumericalValue = int(Arr_Network[1]);
         if(this.Title == "测试网络连接" || this.Title == "00")
         {
            Gaming.uiGroup.alertBox.showSuccess("网络连接测试开始!");
         }
         if(this.Title == "获取服务器时间" || this.Title == "01")
         {
            var result1:String = Gaming.testCtrl.cheating.other.testSeverTime("",this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("服务器时间测试开始，次数：" + this.NumericalValue);
         }
         if(this.Title == "测试排行榜" || this.Title == "02")
         {
            var result2:String = Gaming.testCtrl.cheating.other.testTop("",this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("排行榜测试开始，分数：" + this.NumericalValue);
         }
         if(this.Title == "获取排行榜数据" || this.Title == "03")
         {
            var result3:String = Gaming.testCtrl.cheating.other.getTopData(this.Name,0);
            Gaming.uiGroup.alertBox.showSuccess("获取排行榜数据：" + this.Name);
         }
         if(this.Title == "设置模拟分数" || this.Title == "04")
         {
            var result4:String = Gaming.testCtrl.cheating.other.setSimTopScore("",this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess(result4);
         }
         if(this.Title == "MD5加密" || this.Title == "05")
         {
            var result5:String = Gaming.testCtrl.cheating.other.md5(this.Name,0);
            Gaming.uiGroup.alertBox.showSuccess("MD5结果：" + result5);
         }
      }

      public function CurrencyCheating(str0:String) : void
      {
         var Arr_Currency:Array = new Array();
         Arr_Currency = str0.split("*",str0.length);
         this.Title = Arr_Currency[0];
         this.NumericalValue = int(Arr_Currency[1]);

         if(this.Title == "设置纪念币" || this.Title == "00")
         {
            Gaming.PG.da.main.save.anniCoin = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置纪念币为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         else if(this.Title == "设置十年币" || this.Title == "01")
         {
            Gaming.PG.da.main.save.tenCoin = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置十年币为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         else if(this.Title == "设置零件券" || this.Title == "02")
         {
            Gaming.PG.da.main.save.partsC = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置零件券为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         else if(this.Title == "设置粽子" || this.Title == "03")
         {
            Gaming.PG.da.main.save.setActivePrice(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置粽子为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         else if(this.Title == "设置银币" || this.Title == "04")
         {
            Gaming.PG.da.main.save.coin = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置银币为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         else if(this.Title == "设置积分" || this.Title == "05")
         {
            Gaming.PG.da.main.save.score = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置积分为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         else if(this.Title == "设置黄金" || this.Title == "06")
         {
            Gaming.api.shop.test_balance = Number(this.NumericalValue);
            Gaming.api.shop.test_totalRecharged = Number(this.NumericalValue);
            PayCtrl.getBalance();
            Gaming.uiGroup.alertBox.showSuccess("设置黄金为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         else if(this.Title == "设置万能球" || this.Title == "07")
         {
            Gaming.PG.da.thingsBag.setThingsNum("demBall", this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置万能球为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         else if(this.Title == "黄金充值" || this.Title == "08")
         {
            Gaming.api.shop.payMoney(Number(this.NumericalValue));
            PayCtrl.getBalance();
            Gaming.uiGroup.alertBox.showSuccess("充值" + ComMethod.color(this.NumericalValue,"#fd397b") + "个黄金成功!");
         }
         else if(this.Title == "设置小南瓜" || this.Title == "09")
         {
            Gaming.PG.da.main.save.pumpkin22 = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置小南瓜为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }

         // 刷新UI显示
         UIShow.flesh_coinChange();
      }
   }
}

